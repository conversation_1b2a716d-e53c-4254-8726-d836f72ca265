import { AccountConnectModal } from "../modals/AccountConnectModal";
import { useGame } from "@/contexts/GameContext";
import Header from "./Header";

const Layout = ({ children }) => {
  const { showAccountConnectModal, setShowAccountConnectModal } = useGame();

  return (
    <div className="min-h-screen bg-dark-bg">
      <Header />
      {children}

      {/* Account Connect Modal */}
      <AccountConnectModal
        isOpen={showAccountConnectModal}
        onClose={() => setShowAccountConnectModal(false)}
      />
    </div>
  );
};

export default Layout;
