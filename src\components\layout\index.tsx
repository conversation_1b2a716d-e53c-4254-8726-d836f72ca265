import { AccountConnectModal } from "../modals/AccountConnectModal";
import { useGame } from "@/contexts/GameContext";
import { toast } from "@/hooks/use-toast";
import Header from "./Header";

const Layout = ({ children }) => {
  const { setAccountCredentials, showAccountConnectModal, setShowAccountConnectModal } = useGame();

  // Handle Account Token submission
  const handleAccountConnectSubmit = (token: string, password: string) => {
    setAccountCredentials(token, password);
    setShowAccountConnectModal(false);
    toast({
      title: "Account Connected",
      description: "Your progress will now be tracked!",
    });
  };

  return (
    <div className="min-h-screen bg-dark-bg">
      <Header />
      {children}

      {/* Account Token Modal */}
      <AccountConnectModal
        isOpen={showAccountConnectModal}
        onClose={() => setShowAccountConnectModal(false)}
        onSubmit={handleAccountConnectSubmit}
      />
    </div>
  );
};

export default Layout;
