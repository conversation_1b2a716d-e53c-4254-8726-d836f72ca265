
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				// JargonQuest Gaming Colors
				'primary-blue': '#1E40AF',
				'accent-cyan': '#06B6D4',
				'gaming-gold': '#F59E0B',
				'success-green': '#10B981',
				'deep-space': '#0F172A',
				'card-dark': '#1E293B',
				'surface-dark': '#334155',
				'overlay': '#475569',
				'primary-text': '#F8FAFC',
				'secondary-text': '#CBD5E1',
				'muted-text': '#94A3B8',
				'accent-text': '#06B6D4',
				'active-green': '#22C55E',
				'inactive-red': '#EF4444',
				'warning-orange': '#F97316',
				'info-blue': '#3B82F6',
				'border-light': '#475569',
				'border-dark': '#334155',
				'glow-blue': '#3B82F6',
				'glow-cyan': '#06B6D4',
				'coin-gold': '#FCD34D',
				'xp-purple': '#8B5CF6',
				'rare-blue': '#3B82F6',
				'epic-purple': '#A855F7',
				'legendary-orange': '#F97316',
				'nav-background': '#1E293B',
				'nav-active': '#F59E0B',
				'nav-inactive': '#64748B',
				'nav-border': '#334155',
				'binance-gold': '#F0B90B',
				'ethereum-blue': '#627EEA',
				'solana-purple': '#9945FF',
				'wallet-green': '#10B981',
				'health-red': '#DC2626',
				'shield-blue': '#2563EB',
				'speed-yellow': '#EAB308',
				'crystal-purple': '#7C3AED',
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'scale-in': {
					'0%': {
						transform: 'scale(0.95)',
						opacity: '0'
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '1'
					}
				},
				'slide-up': {
					'0%': {
						transform: 'translateY(20px)',
						opacity: '0'
					},
					'100%': {
						transform: 'translateY(0)',
						opacity: '1'
					}
				},
				'counter-up': {
					'0%': {
						transform: 'translateY(20px)',
						opacity: '0'
					},
					'100%': {
						transform: 'translateY(0)',
						opacity: '1'
					}
				},
				'glow': {
					'0%, 100%': {
						boxShadow: '0 0 5px #1E40AF'
					},
					'50%': {
						boxShadow: '0 0 20px #1E40AF, 0 0 30px #1E40AF'
					}
				},
				'cyan-glow': {
					'0%, 100%': {
						boxShadow: '0 0 5px #06B6D4'
					},
					'50%': {
						boxShadow: '0 0 20px #06B6D4, 0 0 30px #06B6D4'
					}
				},
				'float': {
					'0%, 100%': {
						transform: 'translateY(0px)'
					},
					'50%': {
						transform: 'translateY(-10px)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.3s ease-out',
				'scale-in': 'scale-in 0.2s ease-out',
				'slide-up': 'slide-up 0.4s ease-out',
				'counter-up': 'counter-up 0.6s ease-out',
				'glow': 'glow 2s ease-in-out infinite',
				'cyan-glow': 'cyan-glow 2s ease-in-out infinite',
				'float': 'float 3s ease-in-out infinite'
			},
			fontFamily: {
				'mono': ['JetBrains Mono', 'monospace'],
			},
			backgroundImage: {
				'primary-gradient': 'linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%)',
				'avatar-gradient': 'linear-gradient(135deg, #06B6D4 0%, #10B981 100%)',
				'card-gradient': 'linear-gradient(135deg, #1E293B 0%, #334155 100%)',
				'gold-gradient': 'linear-gradient(135deg, #F59E0B 0%, #EAB308 100%)',
				'gaming-gradient': 'linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%)',
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
