import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GameApiService } from '@/services/game-api';
import { Game, UserStats } from '@/types/api';

// Query Keys
export const QUERY_KEYS = {
  GAMES: ['games'] as const,
  USER_STATS: (gameId: string, accountToken: string, password: string) => ['user-stats', gameId, accountToken, password] as const,
} as const;

// Hook to fetch games
export const useGames = () => {
  return useQuery({
    queryKey: QUERY_KEYS.GAMES,
    queryFn: GameApiService.getGames,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
};

// Hook to fetch user stats
export const useUserStats = (gameId: string, accountToken: string, password: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.USER_STATS(gameId, accountToken, password),
    queryFn: () => GameApiService.getUserStats(gameId, accountToken, password),
    enabled: enabled && !!gameId && !!accountToken && !!password,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook to update score
export const useUpdateScore = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ gameId, score, accountToken, password }: { gameId: string; score: number; accountToken: string; password: string }) =>
      GameApiService.updateScore(gameId, score, accountToken, password),
    onSuccess: (data, variables) => {
      // Update the user stats cache with the new data
      queryClient.setQueryData(
        QUERY_KEYS.USER_STATS(variables.gameId, variables.accountToken, variables.password),
        data
      );

      // Optionally invalidate to refetch from server
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.USER_STATS(variables.gameId, variables.accountToken, variables.password),
      });
    },
    onError: (error) => {
      console.error('Failed to update score:', error);
    },
  });
};
