import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GameApiService } from '@/services/game-api';
import { Game, UserStats } from '@/types/api';

// Query Keys
export const QUERY_KEYS = {
  GAMES: ['games'] as const,
  CONNECT_ACCOUNT: (token: string, password: string) => ['connect-account', token, password] as const,
  CONNECT_ACCOUNT_WITH_AUTH: (authToken: string) => ['connect-account-with-auth', authToken] as const,
} as const;

// Hook to fetch games
export const useGames = () => {
  return useQuery({
    queryKey: QUERY_KEYS.GAMES,
    queryFn: GameApiService.getGames,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
};

// Hook to connect account with token and password
export const useConnectAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ token, password }: { token: string; password: string }) =>
      GameApiService.connectAccount(token, password),
    onSuccess: (data) => {
      // Cache the connection data
      queryClient.setQueryData(
        QUERY_KEYS.CONNECT_ACCOUNT(data.user_data._id, 'connected'),
        data
      );
    },
    onError: (error) => {
      console.error('Failed to connect account:', error);
    },
  });
};

// Hook to connect account with existing auth token
export const useConnectAccountWithAuth = (authToken: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.CONNECT_ACCOUNT_WITH_AUTH(authToken),
    queryFn: () => GameApiService.connectAccountWithAuth(authToken),
    enabled: enabled && !!authToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: false, // Don't retry if auth token is invalid
  });
};

// Hook to update score
export const useUpdateScore = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ gameId, score, authToken }: { gameId: string; score: number; authToken: string }) =>
      GameApiService.updateScore(gameId, score, authToken),
    onSuccess: (data, variables) => {
      // Invalidate connect account with auth to refresh game stats
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.CONNECT_ACCOUNT_WITH_AUTH(variables.authToken),
      });
    },
    onError: (error) => {
      console.error('Failed to update score:', error);
    },
  });
};
