Enhanced JargonQuest GamePlay Styles

.gameplayHeader {
  position: relative;
  overflow: hidden;
}

/* Enhanced game container */
.game-container {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 1.5rem;
  overflow: hidden;
  position: relative;
}

.game-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.05) 0%, 
    rgba(139, 92, 246, 0.03) 50%, 
    rgba(245, 158, 11, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

/* Enhanced game header */
.game-header {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(6, 182, 212, 0.3);
  position: relative;
}

.game-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(6, 182, 212, 0.5), 
    transparent);
  animation: header-glow 3s ease-in-out infinite;
}

/* Enhanced JargonQuest GamePlay Styles */

.gameplayHeader {
  position: relative;
  overflow: hidden;
}

/* Enhanced game container */
.game-container {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 1.5rem;
  overflow: hidden;
  position: relative;
}

.game-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.05) 0%, 
    rgba(139, 92, 246, 0.03) 50%, 
    rgba(245, 158, 11, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

/* Enhanced game header */
.game-header {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(6, 182, 212, 0.3);
  position: relative;
}

.game-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(6, 182, 212, 0.5), 
    transparent);
  animation: header-glow 3s ease-in-out infinite;
}

/* Enhanced stat cards */
.stat-card {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(71, 85, 105, 0.6) 100%);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  border-color: rgba(6, 182, 212, 0.4);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 25px rgba(6, 182, 212, 0.2);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(6, 182, 212, 0.1), 
    transparent);
  transition: left 0.6s ease;
}

.stat-card:hover::before {
  left: 100%;
}

/* Enhanced game canvas */
.game-canvas {
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.9) 0%, 
    rgba(30, 64, 175, 0.2) 25%, 
    rgba(6, 182, 212, 0.1) 75%, 
    rgba(15, 23, 42, 0.9) 100%);
  position: relative;
  overflow: hidden;
}

.game-canvas::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(6, 182, 212, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
  animation: canvas-pulse 6s ease-in-out infinite;
  pointer-events: none;
}

/* Enhanced floating particles */
.floating-particle {
  position: absolute;
  pointer-events: none;
  animation: particle-float 4s ease-in-out infinite;
}

.floating-particle.cyan {
  background: radial-gradient(circle, #06B6D4 0%, transparent 70%);
}

.floating-particle.purple {
  background: radial-gradient(circle, #8B5CF6 0%, transparent 70%);
}

.floating-particle.gold {
  background: radial-gradient(circle, #F59E0B 0%, transparent 70%);
}

/* Enhanced sidebar */
.game-sidebar {
  background: linear-gradient(180deg, 
    rgba(30, 41, 59, 0.95) 0%, 
    rgba(51, 65, 85, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(6, 182, 212, 0.3);
  position: relative;
}

.game-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(180deg, 
    transparent, 
    rgba(6, 182, 212, 0.5), 
    transparent);
  animation: sidebar-glow 4s ease-in-out infinite;
}

/* Enhanced sidebar cards */
.sidebar-card {
  background: linear-gradient(135deg, 
    rgba(51, 65, 85, 0.8) 0%, 
    rgba(71, 85, 105, 0.4) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.sidebar-card:hover {
  border-color: rgba(6, 182, 212, 0.4);
  background: linear-gradient(135deg, 
    rgba(51, 65, 85, 0.9) 0%, 
    rgba(71, 85, 105, 0.5) 100%);
}

.sidebar-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.05) 0%, 
    rgba(139, 92, 246, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  pointer-events: none;
}

.sidebar-card:hover::before {
  opacity: 1;
}

/* Enhanced leaderboard items */
.leaderboard-item {
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 1rem;
  padding: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.leaderboard-item:hover {
  background: rgba(51, 65, 85, 0.6);
  border-color: rgba(6, 182, 212, 0.3);
  transform: translateX(4px);
}

.leaderboard-item.current-player {
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.2) 0%, 
    rgba(139, 92, 246, 0.2) 100%);
  border-color: rgba(6, 182, 212, 0.4);
}

.leaderboard-item.current-player::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #06B6D4 0%, #8B5CF6 100%);
  border-radius: 0 2px 2px 0;
}

/* Enhanced rank badges */
.rank-badge {
  width: 2rem;
  height: 2rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  font-size: 0.875rem;
  position: relative;
  transition: all 0.3s ease;
}

.rank-badge.first {
  background: linear-gradient(135deg, #F59E0B 0%, #F97316 100%);
  color: #0F172A;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
  animation: rank-glow 2s ease-in-out infinite;
}

.rank-badge.second {
  background: linear-gradient(135deg, #94A3B8 0%, #64748B 100%);
  color: #0F172A;
  box-shadow: 0 0 20px rgba(148, 163, 184, 0.4);
}

.rank-badge.third {
  background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
  color: #0F172A;
  box-shadow: 0 0 20px rgba(249, 115, 22, 0.4);
}

/* Enhanced game controls */
.control-item {
  background: rgba(51, 65, 85, 0.4);
  border-radius: 0.5rem;
  padding: 0.75rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.control-item:hover {
  background: rgba(51, 65, 85, 0.6);
  transform: translateX(2px);
}

.control-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(6, 182, 212, 0.6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.control-item:hover::before {
  opacity: 1;
}

/* Enhanced power-up indicators */
.power-up-indicator {
  background: linear-gradient(135deg, #F97316 0%, #F59E0B 100%);
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: power-up-pulse 1.5s ease-in-out infinite;
}

.power-up-indicator::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #F97316 0%, #F59E0B 100%);
  border-radius: 50%;
  opacity: 0.5;
  animation: power-up-ring 2s ease-in-out infinite;
  z-index: -1;
}

/* Enhanced combo indicator */
.combo-indicator {
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
  border: 2px solid rgba(139, 92, 246, 0.5);
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  position: relative;
  animation: combo-pulse 0.8s ease-in-out infinite;
}

.combo-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
  border-radius: inherit;
  filter: blur(10px);
  opacity: 0.6;
  z-index: -1;
  animation: combo-glow 1s ease-in-out infinite;
}

/* Animations */
@keyframes header-glow {
  0%, 100% { opacity: 0.3; transform: scaleX(0.5); }
  50% { opacity: 0.8; transform: scaleX(1); }
}

@keyframes sidebar-glow {
  0%, 100% { opacity: 0.3; transform: scaleY(0.5); }
  50% { opacity: 0.8; transform: scaleY(1); }
}

@keyframes canvas-pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

@keyframes particle-float {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) rotate(0deg); 
    opacity: 0.3;
  }
  25% { 
    transform: translateY(-10px) translateX(5px) rotate(90deg); 
    opacity: 0.7;
  }
  50% { 
    transform: translateY(-15px) translateX(-5px) rotate(180deg); 
    opacity: 1;
  }
  75% { 
    transform: translateY(-5px) translateX(3px) rotate(270deg); 
    opacity: 0.7;
  }
}

@keyframes rank-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(245, 158, 11, 0.8);
  }
}

@keyframes power-up-pulse {
  0%, 100% { 
    transform: scale(1); 
    filter: brightness(1);
  }
  50% { 
    transform: scale(1.1); 
    filter: brightness(1.2);
  }
}

@keyframes power-up-ring {
  0% { 
    transform: scale(1); 
    opacity: 0.5;
  }
  100% { 
    transform: scale(1.5); 
    opacity: 0;
  }
}

@keyframes combo-pulse {
  0%, 100% { 
    transform: scale(1); 
  }
  50% { 
    transform: scale(1.05); 
  }
}

@keyframes combo-glow {
  0%, 100% { 
    opacity: 0.6; 
    filter: blur(10px);
  }
  50% { 
    opacity: 0.9; 
    filter: blur(15px);
  }
}

@keyframes score-bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes progress-fill {
  0% { width: 0%; }
  100% { width: var(--progress-width); }
}

/* Enhanced button styles */
.game-button {
  background: linear-gradient(135deg, #06B6D4 0%, #8B5CF6 100%);
  border: none;
  border-radius: 1rem;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.game-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 10px 25px rgba(6, 182, 212, 0.4);
}

.game-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  transition: left 0.5s ease;
}

.game-button:hover::before {
  left: 100%;
}

.game-button.primary {
  background: linear-gradient(135deg, #F59E0B 0%, #F97316 100%);
}

.game-button.secondary {
  background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
}

.game-button.danger {
  background: linear-gradient(135deg, #DC2626 0%, #EF4444 100%);
}

/* Responsive design */
@media (max-width: 1024px) {
  .game-sidebar {
    width: 280px;
  }
  
  .stat-card {
    padding: 0.75rem;
  }
  
  .sidebar-card {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .game-sidebar {
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    z-index: 50;
    transition: right 0.3s ease;
  }
  
  .game-sidebar.open {
    right: 0;
  }
  
  .stat-card:hover,
  .sidebar-card:hover,
  .leaderboard-item:hover {
    transform: none;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .stat-card,
  .sidebar-card,
  .leaderboard-item,
  .game-button,
  .power-up-indicator,
  .combo-indicator {
    animation: none;
    transition: none;
  }
  
  .floating-particle {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .stat-card,
  .sidebar-card,
  .leaderboard-item {
    border-width: 2px;
    border-color: #06B6D4;
  }
  
  .rank-badge {
    border: 2px solid #06B6D4;
  }
}

/* Focus styles for accessibility */
.game-button:focus,
.stat-card:focus-within,
.sidebar-card:focus-within {
  outline: 2px solid #06B6D4;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .game-container,
  .stat-card,
  .sidebar-card {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
  
  .game-button {
    background: #f5f5f5 !important;
    color: #333 !important;
  }
}