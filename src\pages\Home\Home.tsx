import { <PERSON> } from 'react-router-dom';
import { <PERSON>pad2, Trophy, Users, Coins, ArrowRight, Play, Star, Zap, Sparkles, Rocket, Shield, Target, Crown, Globe, Loader2, AlertCircle } from 'lucide-react';
import { useWallet } from '@/contexts/WalletContext';
import { useGame } from '@/contexts/GameContext';
import { useGames } from '@/hooks/use-game-api';
import { Game } from '@/types/api';
import { Button } from '@/components/ui/button';
import AnimatedCounter from '@/components/ui/AnimatedCounter';
import { gameUrls } from '@/game-data';
import styles from './Home.module.css';

const Home = () => {
  const { isConnected, connectWallet } = useWallet();
  const { setSelectedGame } = useGame();
  
  // Fetch games from API
  const { data: apiGames, isLoading, error } = useGames();

  // Transform API games to featured games format
  const featuredGames = apiGames // Filter for featured games
    ?.slice(0, 3) // Take first 3 featured games
    ?.map((game: Game, index: number) => ({
      id: game._id,
      gameId: game.game_id,
      title: game.title,
      description: game.description,
      image: game.image,
      blockchain: game.blockchain,
      rewards: `${game.min_reward}-${game.max_reward} JQ`,
      players: `${Math.floor(Math.random() * 20)}K`, // Generate random player count for display
      difficulty: game.difficulty,
      status: 'Live',
      gradient: getGameGradient(game.blockchain),
      borderGlow: getBlockchainColor(game.blockchain),
      gameUrl: gameUrls[game.game_id],
    })) || [];

  // Fallback featured games if no featured games from API or if still loading
  const fallbackFeaturedGames = [
    {
      id: 1,
      gameId: 'cyber-runner',
      title: 'Cyber Runner Elite',
      description: 'Navigate neon-lit cyberpunk cities, collect rare digital artifacts, and compete in the ultimate endless runner experience',
      image: '🏃‍♂️',
      blockchain: 'BSC',
      rewards: '50-200 JQ',
      players: '12.5K',
      difficulty: 'Easy',
      status: 'Live',
      gradient: 'from-binance-gold/30 via-gaming-gold/20 to-coin-gold/10',
      borderGlow: 'binance-gold',
    },
    {
      id: 2,
      gameId: 'nft-puzzle',
      title: 'NFT Puzzle Matrix',
      description: 'Solve mind-bending puzzles in a digital realm where each victory mints unique NFTs and unlocks hidden treasures',
      image: '🧩',
      blockchain: 'SOL',
      rewards: '25-100 JQ',
      players: '8.2K',
      difficulty: 'Medium',
      status: 'Live',
      gradient: 'from-solana-purple/30 via-xp-purple/20 to-epic-purple/10',
      borderGlow: 'solana-purple',
    },
    {
      id: 3,
      gameId: 'defi-defense',
      title: 'DeFi Defense Wars',
      description: 'Strategic tower defense meets yield farming. Defend your assets while earning passive income in real-time battles',
      image: '🏰',
      blockchain: 'ETH',
      rewards: '100-500 JQ',
      players: '6.8K',
      difficulty: 'Hard',
      status: 'Beta',
      gradient: 'from-ethereum-blue/30 via-primary-blue/20 to-rare-blue/10',
      borderGlow: 'ethereum-blue',
    },
  ];

  // Use API games if available, otherwise use fallback
  const displayedFeaturedGames = featuredGames.length > 0 ? featuredGames : fallbackFeaturedGames;

  // Helper functions
  function getGameGradient(blockchain: string) {
    const gradients = {
      BSC: 'from-binance-gold/30 via-gaming-gold/20 to-coin-gold/10',
      SOL: 'from-solana-purple/30 via-xp-purple/20 to-epic-purple/10',
      ETH: 'from-ethereum-blue/30 via-primary-blue/20 to-rare-blue/10',
      AVAX: 'from-red-500/30 via-red-400/20 to-red-300/10',
      MATIC: 'from-purple-500/30 via-purple-400/20 to-purple-300/10',
    };
    return gradients[blockchain as keyof typeof gradients] || 'from-accent-cyan/30 via-xp-purple/20 to-gaming-gold/10';
  }

  function getBlockchainColor(blockchain: string) {
    const colors = {
      BSC: 'binance-gold',
      SOL: 'solana-purple',
      ETH: 'ethereum-blue',
      AVAX: 'red-500',
      MATIC: 'purple-500',
    };
    return colors[blockchain as keyof typeof colors] || 'accent-cyan';
  }

  // Handle game selection
  const handleGameSelect = (game: any) => {
    // Convert the local game format back to API format for context
    const apiGame: Game = {
      _id: game.id,
      game_id: game.gameId,
      title: game.title,
      description: game.description,
      image: game.image,
      blockchain: game.blockchain,
      category: 'Action', // Default category
      min_reward: parseInt(game.rewards.split('-')[0]),
      max_reward: parseInt(game.rewards.split('-')[1].replace(' JQ', '')),
      difficulty: game.difficulty,
      featured: true,
      trending: false,
      game_url: game.gameUrl,
    };
    setSelectedGame(apiGame);
  };

  const features = [
    {
      icon: Trophy,
      title: 'Earn Legendary Rewards',
      description: 'Battle through epic quests and tournaments to earn crypto rewards, rare NFTs, and exclusive badges that showcase your gaming prowess.',
      color: 'text-gaming-gold',
      gradient: 'from-gaming-gold/20 via-legendary-orange/10 to-coin-gold/20',
      bgIcon: '🏆',
    },
    {
      icon: Users,
      title: 'Guild & Community Power',
      description: 'Form alliances, join guilds, and dominate global leaderboards with friends. Your community strength multiplies your rewards.',
      color: 'text-accent-cyan',
      gradient: 'from-accent-cyan/20 via-success-green/10 to-wallet-green/20',
      bgIcon: '⚔️',
    },
    {
      icon: Zap,
      title: 'Lightning Fast Payouts',
      description: 'Instant reward distribution across multiple blockchains with minimal fees. Your victories translate to immediate crypto gains.',
      color: 'text-xp-purple',
      gradient: 'from-xp-purple/20 via-epic-purple/10 to-crystal-purple/20',
      bgIcon: '⚡',
    },
  ];

  const achievements = [
    { label: 'Legends Online', value: 156789, suffix: '', icon: '🎮', color: 'accent-cyan' },
    { label: 'Epic Victories', value: 2456789, suffix: '', icon: '⚔️', color: 'success-green' },
    { label: 'Treasure Earned', value: 892.5, suffix: 'K BNB', icon: '💎', color: 'gaming-gold' },
    { label: 'Guild Champions', value: 45230, suffix: '', icon: '👑', color: 'xp-purple' },
  ];

  return (
    <div className="min-h-screen relative overflow-hidden bg-slate-950">
      {/* Enhanced Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
        <div className="absolute inset-0 digital-grid opacity-20"></div>

        {/* Floating gaming elements */}
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute opacity-10 pointer-events-none"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float-advanced ${4 + Math.random() * 6}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 3}s`,
            }}
          >
            <div
              className={`w-32 h-32 rounded-full`}
              style={{
                background: `radial-gradient(circle, ${['#06B6D4', '#8B5CF6', '#F59E0B', '#10B981', '#F97316'][i % 5]
                  }40, transparent 70%)`,
              }}
            />
          </div>
        ))}

        {/* Gaming particles */}
        {[...Array(12)].map((_, i) => (
          <div
            key={`particle-${i}`}
            className="absolute w-2 h-2 bg-accent-cyan rounded-full opacity-60"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `particle-move ${15 + Math.random() * 10}s linear infinite`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      {/* Hero Section - Enhanced */}
      <section className="relative py-16 px-4 sm:px-6 lg:px-8 min-h-screen flex items-center">
        <div className="relative max-w-7xl mx-auto text-center w-full">
          <div className="animate-fade-in">
            {/* Enhanced title with multiple effects */}
            <div className="relative mb-6">
              <div className="relative text-5xl md:text-7xl text-xl md:text-2xl font-bold text-accent-cyan mb-4">
                <span className="inline-flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Play • Earn • Dominate
                  <Sparkles className="w-5 h-5" />
                </span>
              </div>
            </div>

            <p className="text-lg md:text-xl text-secondary-text mb-8 max-w-4xl mx-auto leading-relaxed">
              Enter the <span className="text-accent-cyan font-bold bg-accent-cyan/10 px-2 py-1 rounded">ultimate Web3 gaming multiverse</span> where
              every move earns rewards, every victory builds your legend, and your community drives the revolution forward.
            </p>

            {/* Enhanced CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              {isConnected ? (
                <Button asChild size="lg" className="group relative overflow-hidden bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold text-base px-10 py-4 rounded-2xl transition-all duration-500 shadow-2xl w-64">
                  <Link to="/games">
                    <div className="absolute inset-0 bg-gradient-to-r from-legendary-orange to-gaming-gold opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative flex items-center justify-center gap-3">
                      <Rocket className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
                      Launch Into Battle
                    </span>
                  </Link>
                </Button>
              ) : (
                <Button
                  onClick={connectWallet}
                  size="lg"
                  className="group relative overflow-hidden bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold text-base px-10 py-4 rounded-2xl transition-all duration-500 hover:shadow-accent-cyan/50 w-64"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-xp-purple to-accent-cyan opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <span className="relative flex items-center justify-center gap-3">
                    <Sparkles className="w-5 h-5 group-hover:rotate-180 transition-transform duration-500" />
                    Connect & Conquer
                  </span>
                </Button>
              )}

              <Button asChild variant="outline" size="lg" className="group bg-transparent border-2 border-accent-cyan text-accent-cyan hover:bg-accent-cyan hover:text-deep-space font-semibold text-base px-10 py-4 rounded-2xl transition-all duration-300">
                <Link to="/games">
                  <span className="flex items-center gap-3">
                    Explore Universe
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-2 transition-transform duration-300" />
                  </span>
                </Link>
              </Button>
            </div>

            {/* Enhanced Stats Grid - Adjusted for better viewport fit */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-5xl mx-auto">
              {achievements.map((stat, index) => (
                <div
                  key={index}
                  className="group relative bg-gradient-to-br from-card-dark/80 to-surface-dark/60 backdrop-blur-xl border border-border-light/30 rounded-xl p-4 hover:scale-105 transform transition-all duration-500 hover:shadow-2xl"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/5 to-xp-purple/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">{stat.icon}</div>
                    <div className="text-2xl md:text-3xl font-black mb-1">
                      <span className={`text-${stat.color}`}>
                        <AnimatedCounter
                          value={stat.value}
                          duration={2000 + index * 300}
                          suffix={stat.suffix.includes('K') ? '' : ''}
                          decimals={stat.suffix.includes('K') ? 1 : 0}
                        />
                        {stat.suffix.includes('K') && <span className="text-gaming-gold ml-1">{stat.suffix}</span>}
                      </span>
                    </div>
                    <div className="text-xs font-medium text-secondary-text group-hover:text-white transition-colors duration-300">{stat.label}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto relative">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-black mb-6">
              <span>Why JargonQuest?</span>
            </h2>
            <p className="text-lg text-secondary-text max-w-3xl mx-auto leading-relaxed">
              Experience the future of gaming with our revolutionary Web3 platform where blockchain technology
              meets cutting-edge gameplay design
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`group relative overflow-hidden bg-gradient-to-br ${feature.gradient} backdrop-blur-xl border border-border-light/30 rounded-3xl p-8 transition-all duration-500 hover:shadow-2xl`}
                style={{ animationDelay: `${index * 0.3}s` }}
              >
                {/* Background decoration */}
                <div className="absolute top-4 right-4 text-6xl opacity-10 group-hover:opacity-20 transition-opacity duration-500">
                  {feature.bgIcon}
                </div>

                <div className="relative z-10">
                  <div className={`inline-flex items-center justify-center w-20 h-20 ${feature.color} bg-card-dark/80 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <feature.icon className="w-10 h-10" />
                  </div>
                  <h3 className="text-2xl font-bold mb-6 group-hover:text-white transition-colors duration-300">{feature.title}</h3>
                  <p className="text-secondary-text group-hover:text-primary-text transition-colors duration-300 leading-relaxed text-lg">{feature.description}</p>
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-accent-cyan/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Featured Games */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-12 gap-6">
            <div className="max-w-2xl">
              <h2 className="text-4xl md:text-5xl font-black mb-4">
                <span>Featured Arenas</span>
              </h2>
              <p className="text-lg text-secondary-text leading-relaxed">
                Battle in the most epic gaming environments across the metaverse. Each arena offers unique challenges,
                rewards, and opportunities to prove your gaming supremacy.
              </p>
            </div>
            <Button asChild variant="outline" className="group bg-transparent border-2 border-accent-cyan text-accent-cyan hover:bg-accent-cyan hover:text-deep-space font-semibold text-base px-10 py-4 rounded-2xl transition-all duration-300">
              <Link to="/games">
                <span className="flex items-center gap-3">
                  <Globe className="w-4 h-4" />
                  Explore All Arenas
                  <ArrowRight className="w-4 h-4" />
                </span>
              </Link>
            </Button>
          </div>

          {/* Loading State for Featured Games */}
          {isLoading && (
            <div className="flex items-center justify-center py-20">
              <div className="text-center">
                <Loader2 className="h-12 w-12 animate-spin text-accent-cyan mx-auto mb-4" />
                <p className="text-xl text-secondary-text">
                  Loading featured arenas...
                </p>
                <p className="text-sm text-muted-text mt-2">
                  Preparing epic battles for you
                </p>
              </div>
            </div>
          )}

          {/* Error State for Featured Games */}
          {error && (
            <div className="flex items-center justify-center py-20">
              <div className="text-center max-w-md">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-primary-text mb-2">
                  Unable to Load Featured Games
                </h3>
                <p className="text-secondary-text mb-4">
                  We're having trouble connecting to our game servers. Showing default featured games.
                </p>
              </div>
            </div>
          )}

          {/* Featured Games Grid */}
          {!isLoading && (
            <div className="grid lg:grid-cols-3 gap-8">
              {displayedFeaturedGames.map((game, index) => (
                <div
                  key={game.id}
                  className={`group relative overflow-hidden bg-gradient-to-br ${game.gradient} backdrop-blur-xl border border-border-light/30 rounded-3xl p-8 transition-all duration-500 hover:shadow-2xl`}
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  {/* Status badge */}
                  <div className="absolute top-6 right-6 z-20">
                    <span className={`px-3 py-1 text-xs font-bold rounded-full ${game.status === 'Live'
                        ? 'bg-success-green text-white'
                        : 'bg-warning-orange text-deep-space'
                      }`}>
                      {game.status}
                    </span>
                  </div>

                  <div className="relative z-10">
                    {/* Game icon with animation */}
                    <div className="text-center mb-8">
                      <div
                        className="text-8xl mb-4 inline-block group-hover:scale-110 transition-transform duration-500"
                        style={{ animationDelay: `${index * 0.7}s` }}
                      >
                        {game.image}
                      </div>
                      <div className="flex justify-between items-center mb-6">
                        <h3 className="text-2xl font-bold group-hover:text-white transition-colors duration-300">{game.title}</h3>
                        <span className={`px-3 py-1 text-sm font-bold rounded-full bg-${game.blockchain.toLowerCase()}-badge text-white`}>
                          {game.blockchain}
                        </span>
                      </div>
                    </div>

                    <p className="text-secondary-text group-hover:text-primary-text transition-colors duration-300 mb-8 leading-relaxed text-lg">
                      {game.description}
                    </p>

                    {/* Enhanced game stats */}
                    <div className="grid grid-cols-2 gap-4 mb-8">
                      <div className="bg-card-dark/60 rounded-xl p-4 group-hover:bg-card-dark/80 transition-colors duration-300">
                        <div className="flex items-center space-x-3">
                          <Coins className="h-5 w-5 text-gaming-gold" />
                          <div>
                            <div className="text-sm text-secondary-text">Rewards</div>
                            <div className="font-bold text-gaming-gold">{game.rewards}</div>
                          </div>
                        </div>
                      </div>
                      <div className="bg-card-dark/60 rounded-xl p-4 group-hover:bg-card-dark/80 transition-colors duration-300">
                        <div className="flex items-center space-x-3">
                          <Users className="h-5 w-5 text-accent-cyan" />
                          <div>
                            <div className="text-sm text-secondary-text">Players</div>
                            <div className="font-bold text-accent-cyan">{game.players}</div>
                          </div>
                        </div>
                      </div>
                      <div className="bg-card-dark/60 rounded-xl p-4 group-hover:bg-card-dark/80 transition-colors duration-300">
                        <div className="flex items-center space-x-3">
                          <Star className="h-5 w-5 text-xp-purple" />
                          <div>
                            <div className="text-sm text-secondary-text">Difficulty</div>
                            <div className="font-bold text-xp-purple">{game.difficulty}</div>
                          </div>
                        </div>
                      </div>
                      <div className="bg-card-dark/60 rounded-xl p-4 group-hover:bg-card-dark/80 transition-colors duration-300">
                        <div className="flex items-center space-x-3">
                          <Zap className="h-5 w-5 text-success-green" />
                          <div>
                            <div className="text-sm text-secondary-text">Status</div>
                            <div className="font-bold text-success-green">{game.status}</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Button 
                      asChild 
                      className="w-full group/btn relative overflow-hidden bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold py-4 rounded-xl hover:scale-105 transition-all duration-300"
                    >
                      <Link 
                        to={`/game/${game.gameId}`}
                        onClick={() => handleGameSelect(game)}
                      >
                        <span className="flex items-center justify-center gap-3">
                          <Play className="w-5 h-5 group-hover/btn:scale-110 transition-transform duration-300" />
                          Enter Arena
                          <Target className="w-5 h-5 group-hover/btn:rotate-180 transition-transform duration-500" />
                        </span>
                      </Link>
                    </Button>
                  </div>

                  {/* Enhanced holographic overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/10 via-transparent to-xp-purple/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Enhanced Final CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-transparent">
        <div className="absolute inset-0 digital-grid opacity-30"></div>

        {/* Additional background elements */}
        <div className="absolute top-10 left-10 w-32 h-32 bg-accent-cyan/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-xp-purple/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>

        <div className="max-w-4xl mx-auto text-center relative z-10">
          <div className="relative">
            <h1 className="text-4xl md:text-8xl font-black mb-6">
              <span>Ready to Ascend?</span>
            </h1>
            <p className="text-lg md:text-xl text-secondary-text mb-12 max-w-3xl mx-auto leading-relaxed">
              Join the revolution where gaming meets blockchain innovation. Your journey to digital supremacy,
              legendary rewards, and ultimate gaming glory starts with a single click.
            </p>

            {!isConnected && (
              <div className="space-y-6">
                <Button
                  onClick={connectWallet}
                  size="lg"
                  className="group relative overflow-hidden bg-gradient-to-r from-gaming-gold via-legendary-orange to-gaming-gold text-deep-space font-black text-xl px-12 py-6 rounded-2xl transition-all duration-500 shadow-2xl hover:shadow-gaming-gold/1"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-legendary-orange via-gaming-gold to-legendary-orange opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <span className="relative flex items-center gap-4">
                    <Sparkles className="w-6 h-6 group-hover:rotate-180 transition-transform duration-500" />
                    Begin Your Legend
                    <Crown className="w-6 h-6 group-hover:scale-125 transition-transform duration-300" />
                  </span>
                </Button>
                <p className="text-muted-text text-base">
                  Connect your wallet and enter the ultimate gaming multiverse
                </p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;