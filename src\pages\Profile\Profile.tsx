import { useState } from 'react';
import { Edit, Trophy, Star, Gamepad2, Users, TrendingUp, Calendar, Award, Sparkles, Zap, Shield, Target, Flame } from 'lucide-react';

const Profile = () => {
  const [isConnected] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const address = '0x742d35Cc6634C0532925a3b844Bc9e7595f65C';

  const profileData = {
    username: 'CryptoGamer123',
    bio: 'Passionate Web3 gamer and De<PERSON>i enthusiast. Building my way to the top of the leaderboards! 🎮🚀',
    joinDate: 'March 2024',
    totalEarned: 1247.85,
    socialPoints: 3420,
    jqPoints: 8950,
    gamesPlayed: 127,
    achievements: 23,
    globalRank: 156,
    winRate: 78,
    favoriteGame: 'Crypto Runner',
    timeSpent: '48h 32m',
    level: 42,
    xp: 8750,
    xpToNext: 10000,
    streakDays: 7,
    totalWins: 99,
  };

  const achievements = [
    {
      id: 1,
      name: 'First Steps',
      description: 'Play your first game',
      icon: '🎮',
      rarity: 'common',
      unlocked: true,
      unlockedDate: '2024-03-15',
      reward: 50
    },
    {
      id: 2,
      name: 'Speed Demon',
      description: 'Score 10,000 points in Crypto Runner',
      icon: '⚡',
      rarity: 'rare',
      unlocked: true,
      unlockedDate: '2024-04-02',
      reward: 100
    },
    {
      id: 3,
      name: 'Puzzle Master',
      description: 'Complete 50 puzzles in NFT Puzzle',
      icon: '🧩',
      rarity: 'epic',
      unlocked: true,
      unlockedDate: '2024-05-18',
      reward: 250
    },
    {
      id: 4,
      name: 'Community Leader',
      description: 'Get 100 reactions in community chat',
      icon: '👑',
      rarity: 'legendary',
      unlocked: true,
      unlockedDate: '2024-06-10',
      reward: 500
    },
    {
      id: 5,
      name: 'High Roller',
      description: 'Earn 1000+ SP in a single game',
      icon: '💎',
      rarity: 'legendary',
      unlocked: false,
      progress: 87,
      reward: 1000
    },
    {
      id: 6,
      name: 'Marathon Runner',
      description: 'Play for 100 hours total',
      icon: '🏃‍♂️',
      rarity: 'epic',
      unlocked: false,
      progress: 48,
      reward: 300
    },
  ];

  const gameHistory = [
    {
      game: 'Crypto Runner',
      score: 15420,
      reward: 125,
      date: '2 hours ago',
      result: 'completed',
      rank: 3,
      players: 12
    },
    {
      game: 'NFT Puzzle',
      score: 8750,
      reward: 87,
      date: '1 day ago',
      result: 'completed',
      rank: 1,
      players: 8
    },
    {
      game: 'DeFi Defense',
      score: 12300,
      reward: 156,
      date: '2 days ago',
      result: 'completed',
      rank: 5,
      players: 20
    },
    {
      game: 'Blockchain Shooter',
      score: 6890,
      reward: 69,
      date: '3 days ago',
      result: 'completed',
      rank: 7,
      players: 15
    },
  ];

  const statsData = [
    { label: 'Total Matches', value: 127, icon: Gamepad2, color: 'text-blue-500', change: '+12 this week' },
    { label: 'Win Rate', value: `${profileData.winRate}%`, icon: Target, color: 'text-green-500', change: '+3.2% this month' },
    { label: 'Total Earnings', value: `$${profileData.totalEarned}`, icon: Sparkles, color: 'text-yellow-500', change: '+$234.50 this month' },
    { label: 'Current Streak', value: `${profileData.streakDays} days`, icon: Flame, color: 'text-orange-500', change: 'Personal best!' },
  ];

  const getRarityColor = (rarity) => {
    const colors = {
      common: 'bg-gray-500/20 text-gray-400 border-gray-500/50',
      rare: 'bg-blue-500/20 text-blue-400 border-blue-500/50',
      epic: 'bg-purple-500/20 text-purple-400 border-purple-500/50',
      legendary: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50',
    };
    return colors[rarity] || colors.common;
  };

  const getRarityGlow = (rarity) => {
    const glows = {
      common: '',
      rare: 'shadow-[0_0_20px_rgba(59,130,246,0.3)]',
      epic: 'shadow-[0_0_25px_rgba(168,85,247,0.3)]',
      legendary: 'shadow-[0_0_30px_rgba(234,179,8,0.4)]',
    };
    return glows[rarity] || '';
  };

  const AnimatedCounter = ({ value, decimals = 0, prefix = '' }) => {
    return <span>{prefix}{value.toLocaleString(undefined, { minimumFractionDigits: decimals, maximumFractionDigits: decimals })}</span>;
  };

  if (!isConnected) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-slate-950">
        <div className="relative bg-slate-900/90 backdrop-blur-xl border border-slate-800 rounded-xl p-8 max-w-md mx-auto shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-transparent to-purple-600/10 rounded-xl pointer-events-none" />
          <div className="relative text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-blue-500/25">
              <Shield className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Connect Your Wallet
            </h2>
            <p className="text-gray-400 mb-6">
              Please connect your wallet to view your gaming profile and track your achievements.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mt-16 p-4 sm:p-6 lg:p-8 bg-slate-950">
      <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
      <div className="max-w-7xl mx-auto">
        {/* Profile Header */}
        <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-2xl p-8 mb-8 overflow-hidden shadow-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-purple-600/5" />

          <div className="relative">
            <div className="flex flex-col lg:flex-row items-start gap-6">
              {/* Avatar with Level */}
              <div className="relative group flex-shrink-0">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full blur-xl opacity-30 group-hover:opacity-50 transition-opacity" />
                <div className="relative w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-5xl border-4 border-slate-800 shadow-2xl">
                  🎮
                </div>
                <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-slate-900 text-sm font-bold px-3 py-1 rounded-full shadow-lg">
                  Lvl {profileData.level}
                </div>
              </div>

              {/* Profile Info */}
              <div className="flex-1 min-w-0 ">
                <div className="flex items-center gap-3 flex-wrap mb-3">
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    {profileData.username}
                  </h1>
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-3 py-1 rounded-full shadow-lg shadow-blue-500/25 text-sm font-medium flex items-center">
                    <Trophy className="w-4 h-4 mr-1" />
                    Global Rank #{profileData.globalRank}
                  </div>
                  {profileData.streakDays >= 7 && (
                    <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full shadow-lg shadow-orange-500/25 text-sm font-medium flex items-center">
                      <Flame className="w-4 h-4 mr-1" />
                      {profileData.streakDays} Day Streak!
                    </div>
                  )}
                </div>

                <p className="text-gray-400 mb-4">{profileData.bio}</p>

                {/* XP Progress */}
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Experience</span>
                    <span className="text-gray-300">{profileData.xp.toLocaleString()} / {profileData.xpToNext.toLocaleString()} XP</span>
                  </div>
                  <div className="relative h-3 bg-slate-800 rounded-full overflow-hidden">
                    <div
                      className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg shadow-blue-500/25"
                      style={{ width: `${(profileData.xp / profileData.xpToNext) * 100}%` }}
                    />
                  </div>
                </div>

                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="flex items-center gap-2 text-gray-400">
                    <Calendar className="w-4 h-4" />
                    <span>Joined {profileData.joinDate}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-400">
                    <Gamepad2 className="w-4 h-4" />
                    <span>{profileData.gamesPlayed} games played</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-400">
                    <Award className="w-4 h-4" />
                    <span>{profileData.achievements} achievements</span>
                  </div>
                </div>
              </div>

              {/* Right Side - Stats and Button */}
              <div className="flex flex-col gap-4 w-full lg:w-auto">
                {/* Quick Stats Cards */}
                <div className="grid grid-cols-3 gap-3">
                  <div className="bg-slate-800/50 backdrop-blur rounded-xl p-4 border border-slate-700 hover:border-green-500/50 transition-all group min-w-[100px]">
                    <div className="text-xl lg:text-2xl font-bold text-green-400 group-hover:scale-105 transition-transform">
                      <AnimatedCounter value={profileData.totalEarned} decimals={2} prefix="$" />
                    </div>
                    <div className="text-xs text-gray-400 mt-1 whitespace-nowrap">Total Earned</div>
                  </div>
                  <div className="bg-slate-800/50 backdrop-blur rounded-xl p-4 border border-slate-700 hover:border-yellow-500/50 transition-all group min-w-[100px]">
                    <div className="text-xl lg:text-2xl font-bold text-yellow-400 group-hover:scale-105 transition-transform">
                      <AnimatedCounter value={profileData.socialPoints} />
                    </div>
                    <div className="text-xs text-gray-400 mt-1 whitespace-nowrap">Social Points</div>
                  </div>
                  <div className="bg-slate-800/50 backdrop-blur rounded-xl p-4 border border-slate-700 hover:border-blue-500/50 transition-all group min-w-[100px]">
                    <div className="text-xl lg:text-2xl font-bold text-blue-400 group-hover:scale-105 transition-transform">
                      <AnimatedCounter value={profileData.jqPoints} />
                    </div>
                    <div className="text-xs text-gray-400 mt-1 whitespace-nowrap">JQ Points</div>
                  </div>
                </div>

                {/* Edit Button */}
                <button className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg px-4 py-2 font-medium shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105 transition-all flex items-center justify-center">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Profile
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="space-y-6">
          <div className="flex bg-slate-900/50 backdrop-blur border border-slate-800 p-1 rounded-xl">
            {['overview', 'achievements', 'history', 'stats'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`flex-1 px-4 py-2 rounded-lg font-medium transition-all ${activeTab === tab
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                  : 'text-gray-400 hover:text-white'
                  }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid lg:grid-cols-2 gap-6">
                {/* Performance Overview */}
                <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6 overflow-hidden group hover:border-slate-700 transition-all">
                  <h3 className="text-xl font-bold mb-6 flex items-center gap-2 text-white">
                    <TrendingUp className="w-5 h-5 text-green-400" />
                    Performance Overview
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Win Rate</span>
                        <span className="font-semibold text-green-400">{profileData.winRate}%</span>
                      </div>
                      <div className="h-3 bg-slate-800 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-green-500 to-emerald-500 shadow-lg shadow-green-500/25"
                          style={{ width: `${profileData.winRate}%` }}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 pt-4">
                      <div className="bg-slate-800/50 rounded-xl p-4 text-center">
                        <div className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                          {profileData.favoriteGame}
                        </div>
                        <div className="text-sm text-gray-400 mt-1">Favorite Game</div>
                      </div>
                      <div className="bg-slate-800/50 rounded-xl p-4 text-center">
                        <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                          {profileData.timeSpent}
                        </div>
                        <div className="text-sm text-gray-400 mt-1">Time Played</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recent Achievements */}
                <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6 overflow-hidden group hover:border-slate-700 transition-all">
                  <h3 className="text-xl font-bold mb-6 flex items-center gap-2 text-white">
                    <Trophy className="w-5 h-5 text-yellow-400" />
                    Recent Achievements
                  </h3>
                  <div className="space-y-3">
                    {achievements.filter(a => a.unlocked).slice(-3).map((achievement) => (
                      <div key={achievement.id} className={`relative bg-slate-800/50 rounded-xl p-4 border border-slate-700/50 hover:border-slate-600 transition-all ${getRarityGlow(achievement.rarity)}`}>
                        <div className="flex items-center gap-4">
                          <div className="text-3xl">{achievement.icon}</div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-semibold text-white">{achievement.name}</span>
                              <span className={`text-xs px-2 py-1 rounded-full ${getRarityColor(achievement.rarity)}`}>
                                {achievement.rarity}
                              </span>
                            </div>
                            <p className="text-sm text-gray-400">{achievement.description}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-xs text-gray-500">{achievement.unlockedDate}</div>
                            <div className="text-sm font-semibold text-green-400">+{achievement.reward} XP</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {statsData.map((stat, index) => (
                  <div key={index} className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6 overflow-hidden group hover:border-slate-700 transition-all">
                    <div className="relative">
                      <stat.icon className={`w-8 h-8 ${stat.color} mb-3`} />
                      <div className="text-2xl font-bold mb-1 text-white">{stat.value}</div>
                      <div className="text-sm text-gray-400">{stat.label}</div>
                      <div className="text-xs text-green-400 mt-2">{stat.change}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Achievements Tab */}
          {activeTab === 'achievements' && (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className={`relative bg-slate-900/95 backdrop-blur-xl border ${achievement.unlocked ? 'border-green-500/50' : 'border-slate-800'} rounded-xl p-6 overflow-hidden transition-all hover:scale-105 ${getRarityGlow(achievement.rarity)}`}
                >
                  <div className="relative text-center">
                    <div className="text-5xl mb-4">{achievement.icon}</div>
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <h4 className="font-bold text-lg text-white">{achievement.name}</h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${getRarityColor(achievement.rarity)}`}>
                        {achievement.rarity}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400 mb-4">{achievement.description}</p>

                    {achievement.unlocked ? (
                      <div className="space-y-2">
                        <div className="flex items-center justify-center gap-2 text-green-400">
                          <Award className="w-4 h-4" />
                          <span className="text-sm">Unlocked {achievement.unlockedDate}</span>
                        </div>
                        <div className="text-sm font-semibold text-yellow-400">+{achievement.reward} XP</div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Progress</span>
                          <span className="text-gray-300">{achievement.progress}%</span>
                        </div>
                        <div className="h-2 bg-slate-800 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-gradient-to-r from-blue-500 to-purple-600"
                            style={{ width: `${achievement.progress}%` }}
                          />
                        </div>
                        <div className="text-sm text-gray-500">Reward: {achievement.reward} XP</div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Game History Tab */}
          {activeTab === 'history' && (
            <div className="bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6">
              <h3 className="text-xl font-bold mb-6 flex items-center gap-2 text-white">
                <Gamepad2 className="w-5 h-5 text-blue-400" />
                Recent Games
              </h3>
              <div className="space-y-3">
                {gameHistory.map((game, index) => (
                  <div key={index} className="bg-slate-800/50 rounded-xl p-4 border border-slate-700/50 hover:border-slate-600 transition-all">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-2xl shadow-lg shadow-blue-500/25">
                          🎮
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg text-white">{game.game}</h4>
                          <div className="flex items-center gap-4 text-sm text-gray-400">
                            <span>Score: {game.score.toLocaleString()}</span>
                            <span>•</span>
                            <span>Rank: #{game.rank}/{game.players}</span>
                          </div>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="text-lg font-semibold text-green-400">+{game.reward} SP</div>
                        <div className="text-sm text-gray-400">{game.date}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          {activeTab === 'stats' && (
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6 text-center overflow-hidden group hover:border-blue-500/50 transition-all">
                  <Gamepad2 className="w-10 h-10 mx-auto mb-3 text-blue-400" />
                  <div className="text-3xl font-bold mb-1 text-white">
                    <AnimatedCounter value={profileData.gamesPlayed} />
                  </div>
                  <div className="text-sm text-gray-400">Games Played</div>
                </div>

                <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6 text-center overflow-hidden group hover:border-yellow-500/50 transition-all">
                  <Star className="w-10 h-10 mx-auto mb-3 text-yellow-400" />
                  <div className="text-3xl font-bold mb-1 text-white">
                    <AnimatedCounter value={profileData.achievements} />
                  </div>
                  <div className="text-sm text-gray-400">Achievements</div>
                </div>

                <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6 text-center overflow-hidden group hover:border-green-500/50 transition-all">
                  <Trophy className="w-10 h-10 mx-auto mb-3 text-green-400" />
                  <div className="text-3xl font-bold mb-1 text-white">
                    {profileData.winRate}%
                  </div>
                  <div className="text-sm text-gray-400">Win Rate</div>
                </div>

                <div className="relative bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6 text-center overflow-hidden group hover:border-purple-500/50 transition-all">
                  <Users className="w-10 h-10 mx-auto mb-3 text-purple-400" />
                  <div className="text-3xl font-bold mb-1 text-white">
                    #{profileData.globalRank}
                  </div>
                  <div className="text-sm text-gray-400">Global Rank</div>
                </div>
              </div>

              {/* Additional Stats */}
              <div className="bg-slate-900/95 backdrop-blur-xl border border-slate-800 rounded-xl p-6">
                <h3 className="text-xl font-bold mb-6 flex items-center gap-2 text-white">
                  <Zap className="w-5 h-5 text-yellow-400" />
                  Performance Metrics
                </h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-4 text-gray-300">Game Distribution</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Crypto Runner</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 h-2 bg-slate-800 rounded-full overflow-hidden">
                            <div className="h-full bg-gradient-to-r from-blue-500 to-cyan-500" style={{ width: '45%' }} />
                          </div>
                          <span className="text-sm text-gray-300">45%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">NFT Puzzle</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 h-2 bg-slate-800 rounded-full overflow-hidden">
                            <div className="h-full bg-gradient-to-r from-purple-500 to-pink-500" style={{ width: '30%' }} />
                          </div>
                          <span className="text-sm text-gray-300">30%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">DeFi Defense</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 h-2 bg-slate-800 rounded-full overflow-hidden">
                            <div className="h-full bg-gradient-to-r from-green-500 to-emerald-500" style={{ width: '25%' }} />
                          </div>
                          <span className="text-sm text-gray-300">25%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-4 text-gray-300">Recent Performance</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                        <span className="text-gray-400">Best Score Today</span>
                        <span className="font-semibold text-green-400">15,420</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                        <span className="text-gray-400">Avg. Score (7 days)</span>
                        <span className="font-semibold text-blue-400">11,215</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                        <span className="text-gray-400">Total Wins</span>
                        <span className="font-semibold text-yellow-400">{profileData.totalWins}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Profile;