// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://umflrbsoqkdbbbckqugb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtZmxyYnNvcWtkYmJiY2txdWdiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1MTAzMDgsImV4cCI6MjA2NjA4NjMwOH0.Aver-fnmMPGV_EzwYTJ_6U2va5QLLl4GGhtYnUJQq5k";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);