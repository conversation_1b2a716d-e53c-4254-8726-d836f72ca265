/* Enhanced Gaming Card Styles */
.gaming-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(15, 23, 42, 0.8) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(51, 65, 85, 0.5);
  border-radius: 1rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
}

.gaming-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg, 
    transparent, 
    rgba(59, 130, 246, 0.1), 
    rgba(168, 85, 247, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.gaming-card:hover::before {
  left: 100%;
}

.gaming-card:hover {
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.2),
    0 4px 6px -4px rgba(0, 0, 0, 0.1),
    0 0 20px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
}

/* Profile Header Specific Styles */
.profileHeader {
  background: 
    radial-gradient(ellipse at top left, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom right, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(15, 23, 42, 0.9) 100%);
}

/* Achievement Card Hover Effects */
.achievement-card {
  transform-style: preserve-3d;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.achievement-card:hover {
  transform: translateY(-4px) rotateX(5deg);
}

/* Animated Background Patterns */
.animated-bg {
  position: absolute;
  inset: 0;
  opacity: 0.05;
  background-image: 
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 35px,
      rgba(59, 130, 246, 0.1) 35px,
      rgba(59, 130, 246, 0.1) 70px
    );
  animation: slide 20s linear infinite;
}

@keyframes slide {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(70px, 70px);
  }
}

/* Stat Card Animations */
.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.1) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.stat-card:hover::after {
  opacity: 1;
}

/* Progress Bar Animations */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Tab Indicator Animation */
.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #a855f7);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Rarity Glow Effects */
.rarity-common {
  box-shadow: 0 0 20px rgba(156, 163, 175, 0.2);
}

.rarity-rare {
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.3);
}

.rarity-epic {
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.3);
}

.rarity-legendary {
  box-shadow: 0 0 35px rgba(234, 179, 8, 0.4);
  animation: legendary-pulse 2s ease-in-out infinite;
}

@keyframes legendary-pulse {
  0%, 100% {
    box-shadow: 0 0 35px rgba(234, 179, 8, 0.4);
  }
  50% {
    box-shadow: 0 0 50px rgba(234, 179, 8, 0.6);
  }
}

/* Level Badge Animation */
.level-badge {
  animation: level-float 3s ease-in-out infinite;
}

@keyframes level-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3b82f6, #a855f7);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2563eb, #9333ea);
}