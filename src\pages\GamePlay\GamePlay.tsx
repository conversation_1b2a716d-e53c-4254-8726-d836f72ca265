import { useState, useEffect, useRef } from "react";
import { use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import {
  ArrowLeft,
  Maximize,
  Minimize,
  Shield,
  Trophy,
  User,
  AlertCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useWallet } from "@/contexts/WalletContext";
import { useGame } from "@/contexts/GameContext";
import { useUserStats, useUpdateScore } from "@/hooks/use-game-api";
import { AccountTokenModal } from "@/components/modals/AccountConnectModal";
import { toast } from "@/hooks/use-toast";

const GamePlay = () => {
  const { gameId } = useParams();
  const { isConnected } = useWallet();
  const {
    selectedGame,
    accountToken,
    password,
    setAccountCredentials,
    showAccountConnectModal,
    setShowAccountConnectModal,
    hasAccountToken,
  } = useGame();

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [gameScore, setGameScore] = useState(0);
  const [isGameLoaded, setIsGameLoaded] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // API hooks
  const {
    data: userStats,
    isLoading: statsLoading,
    error: statsError,
  } = useUserStats(gameId || "", accountToken || "", password || "", !!gameId && !!accountToken && !!password);
  const updateScoreMutation = useUpdateScore();

  // Use selected game from context or fallback
  const game = selectedGame && {
    id: selectedGame._id,
    title: selectedGame.title,
    description: selectedGame.description,
    blockchain: selectedGame.blockchain,
    category: selectedGame.category,
    image: selectedGame.image,
    gameUrl: selectedGame.game_url,
  };

  // Player stats - use API data or fallback
  const playerStats = userStats
    ? {
        bestScore: userStats.best_score,
        gamesPlayed: userStats.games_played,
        totalEarned: userStats.total_score,
      }
    : {
        bestScore: 0,
        gamesPlayed: 0,
        totalEarned: 0,
      };

  // Check for Account Token when component mounts
  useEffect(() => {
    if (!hasAccountToken) {
      setShowAccountConnectModal(true);
    }
  }, [hasAccountToken, setShowAccountConnectModal]);

  // Handle Account Token submission
  const handleAccountTokenSubmit = (token: string, password: string) => {
    setAccountCredentials(token, password);
    setShowAccountConnectModal(false);
    toast({
      title: "Account Connected",
      description: "Your progress will now be tracked!",
    });
  };

  // Handle score update
  const handleScoreUpdate = (newScore: number) => {
    if (!accountToken || !password || !gameId) {
      toast({
        title: "Cannot Save Score",
        description: "Account credentials are required to save your progress.",
        variant: "destructive",
      });
      return;
    }

    updateScoreMutation.mutate(
      { gameId, score: newScore, accountToken, password },
      {
        onSuccess: (data) => {
          toast({
            title: "Score Updated!",
            description: `New best: ${data.best_score} | Games played: ${data.games_played}`,
          });
        },
        onError: (error) => {
          toast({
            title: "Failed to Save Score",
            description: "Please try again later.",
            variant: "destructive",
          });
          console.error("Score update error:", error);
        },
      }
    );
  };

  // � HANDLE IFRAME LOAD AND FOCUS
  const handleIframeLoad = () => {
    setIsGameLoaded(true);

    // Focus the iframe to make it interactive immediately
    if (iframeRef.current) {
      try {
        // Focus the iframe element
        iframeRef.current.focus();

        // Also try to focus the content window if possible
        if (iframeRef.current.contentWindow) {
          iframeRef.current.contentWindow.focus();
        }

        console.log("🎮 Game loaded and focused");
      } catch (error) {
        console.log("⚠️ Could not focus iframe:", error);
      }
    }
  };

  // �🎯 EXTRACT SCORE WHEN GAME IS OVER
  useEffect(() => {
    const checkGameOver = () => {
      if (!iframeRef.current?.contentWindow || !isGameLoaded) return;

      try {
        // 🔍 ACCESS GAME VARIABLES DIRECTLY FROM IFRAME
        const gameWindow = iframeRef.current.contentWindow as Window & {
          score?: number;
          document?: Document;
        };

        // ⭐ CHECK IF GAME OVER SCREEN IS VISIBLE
        const gameOverElement = gameWindow.document?.getElementsByClassName(
          "endlessrunner-gameover-background"
        )[0] as HTMLElement;
        const isGameOver = gameOverElement?.style.display === "block";

        // 📊 EXTRACT SCORE ONLY WHEN GAME IS OVER
        if (isGameOver) {
          const currentScore = gameWindow.score || 0;
          if (currentScore !== gameScore) {
            setGameScore(currentScore);
            console.log("🏁 Game ended with score:", currentScore);

            // Update score via API
            if (currentScore > 0) {
              handleScoreUpdate(currentScore);
            }
          }
        }
      } catch (error) {
        // Game iframe not ready yet or cross-origin restrictions
        console.log("⏳ Waiting for game to load...");
      }
    };

    // 🔄 CHECK FOR GAME OVER EVERY 1 SECOND
    const interval = setInterval(checkGameOver, 1000);

    return () => clearInterval(interval);
  }, [gameScore, isGameLoaded, accountToken]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);

    // Focus iframe after toggling fullscreen
    setTimeout(() => {
      if (iframeRef.current) {
        try {
          iframeRef.current.focus();
          if (iframeRef.current.contentWindow) {
            iframeRef.current.contentWindow.focus();
          }
        } catch (error) {
          console.log(
            "⚠️ Could not focus iframe after fullscreen toggle:",
            error
          );
        }
      }
    }, 100);
  };

  // Check if game exists
  if (!game) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <div className="fixed inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30 pointer-events-none"></div>
        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-12 text-center max-w-md mx-auto">
            <div className="text-6xl mb-4">🎮</div>
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Game Not Found
            </h2>
            <p className="text-secondary-text mb-6">
              The game you're looking for doesn't exist.
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold"
            >
              <Link to="/games">Back to Games</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!isConnected) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <div className="fixed inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30 pointer-events-none"></div>
        <div className="fixed inset-0 digital-grid opacity-10 pointer-events-none"></div>

        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-12 text-center max-w-md mx-auto hover:border-accent-cyan/30 transition-all duration-300">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="h-12 w-12 text-accent-cyan" />
              </div>
              <div className="text-6xl mb-4">🎮</div>
            </div>
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Connect Your Wallet
            </h2>
            <p className="text-secondary-text mb-6 leading-relaxed">
              You need to connect your wallet to play games and earn epic
              rewards in the gaming multiverse.
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold px-8 py-3 hover:scale-105 transition-all duration-300"
            >
              <Link to="/games">Back to Gaming Hub</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`${
        isFullscreen ? "fixed inset-0 z-50" : "min-h-screen"
      } relative overflow-hidden bg-deep-space`}
    >
      {!isFullscreen && (
        <div>
          {/* Background */}
          <div className="fixed inset-0 overflow-hidden pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
            <div className="absolute inset-0 digital-grid opacity-5"></div>
          </div>

          {/* Game Header */}
          <div className="relative z-10 bg-gradient-to-r from-card-dark/95 to-surface-dark/90 backdrop-blur-xl border-b border-border-light/30 pt-20 pb-4 px-4">
            <div className="max-w-7xl mx-auto flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <Button
                  asChild
                  variant="ghost"
                  size="sm"
                  className="hover:bg-accent-cyan/10 hover:text-accent-cyan transition-all duration-300"
                >
                  <Link to="/games">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Games
                  </Link>
                </Button>
                <div className="flex items-center space-x-4">
                  <div className="text-6xl">{game.image}</div>
                  <div>
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
                      {game.title}
                    </h1>
                    <p className="text-secondary-text">{game.description}</p>
                    <div className="flex items-center gap-3 mt-2">
                      <span className="px-2 py-1 text-xs font-bold bg-binance-gold/20 text-binance-gold rounded-full">
                        {game.blockchain}
                      </span>
                      <span className="px-2 py-1 text-xs font-medium bg-xp-purple/20 text-xp-purple rounded-full">
                        {game.category}
                      </span>
                    </div>
                    {/* Show final score when game is over */}
                    {gameScore > 0 && (
                      <div className="mt-3 p-3 bg-accent-cyan/10 border border-accent-cyan/30 rounded-lg">
                        <p className="text-sm text-accent-cyan font-medium">
                          🏁 Final Score:{" "}
                          <span className="font-bold text-lg">
                            {gameScore.toLocaleString()}
                          </span>
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Game Controls */}
              <div className="flex items-center space-x-4">
                <Button
                  onClick={toggleFullscreen}
                  variant="outline"
                  size="sm"
                  className="border-accent-cyan text-accent-cyan hover:bg-accent-cyan/10"
                >
                  {isFullscreen ? (
                    <Minimize className="h-4 w-4 mr-2" />
                  ) : (
                    <Maximize className="h-4 w-4 mr-2" />
                  )}
                  {isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
                </Button>
                {/* Open in New Tab - Commented out for now */}
                {/* <Button
                  asChild
                  variant="outline"
                  size="sm"
                  className="border-gaming-gold text-gaming-gold hover:bg-gaming-gold/10"
                >
                  <a href={game.gameUrl} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open in New Tab
                  </a>
                </Button> */}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Game Container */}
      <div
        className={`relative z-10 ${
          isFullscreen ? "h-screen" : "h-[calc(100vh-180px)]"
        }`}
      >
        <div
          className={`h-full w-full relative ${!isFullscreen ? "flex" : ""}`}
        >
          {/* Main Game Area */}
          <div className={`relative ${isFullscreen ? "w-full" : "flex-1"}`}>
            {/* Loading indicator */}
            {!isGameLoaded && (
              <div className="absolute inset-0 bg-deep-space flex items-center justify-center z-20">
                <div className="text-center">
                  <div className="w-16 h-16 border-4 border-accent-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-accent-cyan font-medium">
                    Loading Game...
                  </p>
                  <p className="text-secondary-text text-sm mt-2">
                    Click on the game area once loaded to start playing
                  </p>
                </div>
              </div>
            )}

            {/* Game iframe */}
            <iframe
              ref={iframeRef}
              src={game.gameUrl}
              className="w-full h-full border-0 bg-black"
              title={game.title}
              allow="fullscreen; gamepad; microphone; camera"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-pointer-lock"
              onLoad={handleIframeLoad}
              tabIndex={0}
            />
          </div>

          {/* Player Stats Sidebar - Only show when not in fullscreen */}
          {!isFullscreen && (
            <div className="w-80 bg-gradient-to-b from-card-dark/95 to-surface-dark/90 backdrop-blur-xl border-l border-border-light/30 p-6 space-y-6 overflow-y-auto">
              {/* Player Performance */}
              <div className="bg-gradient-to-br from-surface-dark/80 to-border-light/40 backdrop-blur-sm border border-border-light/30 rounded-xl p-4 hover:border-accent-cyan/30 transition-all duration-300">
                <h3 className="font-bold mb-4 flex items-center text-lg">
                  <Trophy className="mr-2 h-5 w-5 text-gaming-gold" />
                  Your Performance
                </h3>
                {statsLoading ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={i}
                        className="flex justify-between items-center"
                      >
                        <div className="h-4 bg-surface-dark/50 rounded w-20 animate-pulse"></div>
                        <div className="h-4 bg-surface-dark/50 rounded w-16 animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                ) : statsError ? (
                  <div className="text-center py-4">
                    <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                    <p className="text-sm text-secondary-text">
                      Failed to load stats
                    </p>
                  </div>
                ) : !hasAccountToken ? (
                  <div className="text-center py-4">
                    <User className="h-8 w-8 text-accent-cyan mx-auto mb-2" />
                    <p className="text-sm text-secondary-text">
                      Connect Account Token to track stats
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-text">Best Score</span>
                      <span className="font-bold text-gaming-gold">
                        {playerStats.bestScore.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-text">Games Played</span>
                      <span className="font-bold text-accent-cyan">
                        {playerStats.gamesPlayed}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-secondary-text">Total Earned</span>
                      <span className="font-bold text-success-green">
                        {playerStats.totalEarned.toLocaleString()} JQ
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Fullscreen overlay controls */}
          {isFullscreen && (
            <div className="absolute top-4 right-4 z-50">
              <Button
                onClick={toggleFullscreen}
                variant="outline"
                size="sm"
                className="bg-black/50 border-accent-cyan text-accent-cyan hover:bg-accent-cyan/10 backdrop-blur-sm"
              >
                <Minimize className="h-4 w-4 mr-2" />
                Exit Fullscreen
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Account Token Modal */}
      <AccountTokenModal
        isOpen={showAccountConnectModal}
        onClose={() => setShowAccountConnectModal(false)}
        onSubmit={handleAccountTokenSubmit}
      />
    </div>
  );
};

export default GamePlay;
