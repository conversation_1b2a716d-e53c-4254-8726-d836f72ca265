// API Response Types
export interface ApiResponse<T> {
  message: string;
  payload: T;
}

// Game Types
export interface Game {
  _id: string;
  game_id: string;
  title: string;
  description: string;
  image: string;
  blockchain: string;
  category: string;
  min_reward: number;
  max_reward: number;
  difficulty: string;
  featured: boolean;
  trending: boolean;
  game_url: string;
}

export interface GamesResponse {
  games: Game[];
}

// User Stats Types
export interface UserStats {
  best_score: number;
  games_played: number;
  total_score: number;
}

// Connect Account Types
export interface ConnectAccountRequest {
  token: string;
  password: string;
}

export interface ConnectAccountResponse {
  message: string;
  payload: {
    user_data: {
      _id: string;
    };
    auth_token: string;
    game_stats: Record<string, UserStats>;
  };
}

// Connect Account With Auth Types (no request body needed, uses auth header)
export interface ConnectAccountWithAuthResponse {
  message: string;
  payload: {
    user_data: {
      _id: string;
    };
    auth_token: string;
    game_stats: Record<string, UserStats>;
  };
}

// Update Score Types (now uses auth header instead of credentials in body)
export interface UpdateScoreRequest {
  game_id: string;
  score: number;
}

export interface UpdateScoreResponse extends UserStats {}

// Error Response Type
export interface ApiError {
  error: string;
}

// API Endpoints
export const API_ENDPOINTS = {
  GAMES: "/games",
  CONNECT_ACCOUNT: "/connect-account",
  CONNECT_ACCOUNT_WITH_AUTH: "/connect-account-with-auth",
  UPDATE_SCORE: "/update-score",
} as const;
