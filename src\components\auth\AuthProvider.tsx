import React, { useEffect } from 'react';
import { useGame } from '@/contexts/GameContext';
import { useConnectAccountWithAuth } from '@/hooks/use-game-api';

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { authToken, setAuthToken, setGameStats, clearAuthToken } = useGame();

  // Automatically connect with auth token if available
  const {
    data: authData,
    error: authError,
    isLoading: isAuthLoading,
  } = useConnectAccountWithAuth(authToken || '', !!authToken);

  useEffect(() => {
    if (authData?.payload) {
      // Update auth token and game stats from server response
      setAuthToken(authData.payload.auth_token);
      setGameStats(authData.payload.game_stats);
    }
  }, [authData, setAuthToken, setGameStats]);

  useEffect(() => {
    if (authError) {
      // If auth token is invalid, clear it
      console.error('Authentication failed:', authError);
      clearAuthToken();
    }
  }, [authError, clearAuthToken]);

  return <>{children}</>;
};
