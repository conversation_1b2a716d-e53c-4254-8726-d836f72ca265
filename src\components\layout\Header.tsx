import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import {
  Gamepad2,
  Menu,
  X,
  Wallet,
  LogOut,
  Settings,
  User,
  Zap,
  Sparkles,
} from "lucide-react";
import { useWallet } from "@/contexts/WalletContext";
import { useGame } from "@/contexts/GameContext";

const Header = () => {
  const location = useLocation();
  const { isConnected, address, connectWallet, disconnectWallet } = useWallet();
  const { openAccountConnectModal } = useGame();

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const navigation = [
    { name: "Home", href: "/", icon: "🏠" },
    { name: "Games", href: "/games", icon: "🎮" },
    { name: "Community", href: "/community", icon: "👥" },
  ];

  const isActive = (path: string) => location.pathname === path;

  const formatWalletAddress = (address: string | null) => {
    if (!address) return "";
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-slate-900/95 backdrop-blur-lg border-b border-slate-800">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="w-10 h-10 bg-cyan-500/20 rounded-lg flex items-center justify-center hover:scale-110 transition-all duration-300">
                <Gamepad2 className="h-6 w-6 text-cyan-400" />
              </div>
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
            </div>
            <span className="text-xl font-bold text-white hidden sm:block">
              JargonQuest
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`group relative overflow-hidden px-5 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                  isActive(item.href)
                    ? "text-white bg-gradient-to-r from-cyan-500/20 to-cyan-600/20 border border-cyan-500/30"
                    : "text-gray-400 hover:text-white hover:bg-slate-800/50"
                }`}
              >
                {/* Active state glow effect */}
                {isActive(item.href) && (
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 to-cyan-600/10 blur-lg -z-10"></div>
                )}

                {/* Content */}
                <span className="relative z-10 flex items-center justify-center space-x-3">
                  <span
                    className={`flex items-center justify-center transition-transform duration-300 ${
                      isActive(item.href)
                        ? "scale-110"
                        : "group-hover:scale-110"
                    }`}
                  >
                    {item.icon}
                  </span>
                  <span className="whitespace-nowrap leading-none">
                    {item.name}
                  </span>
                </span>

                {/* Active state indicator */}
                {isActive(item.href) && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-cyan-400 rounded-full"></div>
                )}
              </Link>
            ))}
          </div>

          {/* Wallet Connection */}
          <div className="flex items-center space-x-4">
            {isConnected ? (
              <div className="relative">
                <button
                  onClick={() => setIsProfileOpen(!isProfileOpen)}
                  className="flex items-center justify-center space-x-3 bg-slate-800/60 border border-slate-700 hover:border-cyan-500/50 transition-all duration-300 w-40 sm:w-48 h-10 rounded-lg px-3"
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-cyan-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-sm font-medium text-gray-300 mx-3 truncate">
                    {formatWalletAddress(address)}
                  </span>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse flex-shrink-0"></div>
                </button>

                {/* Profile Dropdown */}
                {isProfileOpen && (
                  <div className="absolute right-0 mt-2 w-56 bg-slate-900 border border-slate-800 rounded-lg shadow-xl z-50">
                    <div className="p-4 border-b border-slate-800">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-cyan-500 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-white">
                            Connected
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatWalletAddress(address)}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="p-2">
                      <Link
                        to="/profile"
                        className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-slate-800 transition-colors text-gray-300 hover:text-white"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <User className="h-4 w-4" />
                        <span>Profile</span>
                      </Link>
                      <button
                        onClick={() => {
                          setIsProfileOpen(false);
                        }}
                        className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-red-500/20 text-red-400 transition-colors"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Disconnect</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={openAccountConnectModal}
                className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-medium transition-all duration-300 w-40 sm:w-48 h-10 flex items-center justify-center rounded-lg"
              >
                <User className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Connect Account</span>
                <span className="sm:hidden">Connect</span>
              </button>
            )}

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-gray-400 hover:text-white transition-colors"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden absolute top-full left-0 right-0 bg-slate-900/95 backdrop-blur-lg border-b border-slate-800">
            <div className="px-4 py-4 space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-300 ${
                    isActive(item.href)
                      ? "text-white bg-gradient-to-r from-cyan-500/20 to-cyan-600/20 border border-cyan-500/30"
                      : "text-gray-400 hover:text-white hover:bg-slate-800/50"
                  }`}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span className="font-medium">{item.name}</span>
                </Link>
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* Background overlay when profile is open */}
      {isProfileOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsProfileOpen(false)}
        />
      )}
    </header>
  );
};

export default Header;
