@tailwind base;
@tailwind components;
@tailwind utilities;

/* JargonQuest Web3 Gaming Design System - Enhanced */

@layer base {
  :root {
    --background: 15 23 42; /* deep-space */
    --foreground: 248 250 252; /* primary-text */

    --card: 30 41 59; /* card-dark */
    --card-foreground: 248 250 252;

    --popover: 30 41 59;
    --popover-foreground: 248 250 252;

    --primary: 30 64 175; /* primary-blue */
    --primary-foreground: 248 250 252;

    --secondary: 51 65 85; /* surface-dark */
    --secondary-foreground: 248 250 252;

    --muted: 51 65 85;
    --muted-foreground: 148 163 184; /* muted-text */

    --accent: 6 182 212; /* accent-cyan */
    --accent-foreground: 248 250 252;

    --destructive: 239 68 68; /* inactive-red */
    --destructive-foreground: 248 250 252;

    --border: 71 85 105; /* border-light */
    --input: 30 41 59;
    --ring: 30 64 175;

    --radius: 0.75rem;
    
    /* Enhanced gradients */
    --primary-gradient: linear-gradient(135deg, #1E40AF 0%, #3B82F6 50%, #06B6D4 100%);
    --avatar-gradient: linear-gradient(135deg, #06B6D4 0%, #10B981 50%, #8B5CF6 100%);
    --card-gradient: linear-gradient(135deg, #1E293B 0%, #334155 50%, #475569 100%);
    --gold-gradient: linear-gradient(135deg, #F59E0B 0%, #EAB308 30%, #FCD34D 60%, #F59E0B 100%);
    --gaming-gradient: linear-gradient(135deg, #8B5CF6 0%, #06B6D4 50%, #F59E0B 100%);
    
    /* Color variables */
    --xp-purple: #8B5CF6;
    --binance-gold: #F0B90B;
    --solana-purple: #9945FF;
    --ethereum-blue: #627EEA;
    --success-green: #10B981;
    --gaming-gold: #F59E0B;
    --accent-cyan: #06B6D4;
    --primary-blue: #1E40AF;
    --card-dark: #1E293B;
    --surface-dark: #334155;
    --deep-space: #0F172A;
    --border-light: #475569;
    --border-dark: #334155;
    --coin-gold: #FCD34D;
    --rare-blue: #3B82F6;
    --epic-purple: #A855F7;
    --legendary-orange: #F97316;
    --nav-background: #1E293B;
    --nav-active: #F59E0B;
    --nav-inactive: #64748B;
    --nav-border: #334155;
    --wallet-green: #10B981;
    --health-red: #DC2626;
    --shield-blue: #2563EB;
    --speed-yellow: #EAB308;
    --crystal-purple: #7C3AED;
    --warning-orange: #F97316;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-deep-space text-primary-text font-sans;
    background: 
      radial-gradient(circle at 20% 10%, rgba(30, 64, 175, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.12) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 90% 90%, rgba(245, 158, 11, 0.08) 0%, transparent 50%),
      linear-gradient(135deg, #0F172A 0%, #1E293B 25%, #334155 50%, #1E293B 75%, #0F172A 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
  }

  /* Enhanced animated background */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 120%;
    height: 120%;
    background-image: 
      radial-gradient(circle at 2px 2px, rgba(6, 182, 212, 0.15) 1px, transparent 0),
      radial-gradient(circle at 1px 1px, rgba(139, 92, 246, 0.1) 1px, transparent 0);
    background-size: 60px 60px, 40px 40px;
    animation: particle-move 25s linear infinite, particle-drift 30s linear infinite;
    pointer-events: none;
    z-index: 0;
  }

  /* Additional floating particles */
  body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 3px 3px, rgba(245, 158, 11, 0.1) 1px, transparent 0);
    background-size: 80px 80px;
    animation: particle-drift 35s linear infinite reverse;
    pointer-events: none;
    z-index: 0;
  }
}

@layer components {
  /* Enhanced glow effects */
  .glow-effect {
    box-shadow: 
      0 0 20px rgba(30, 64, 175, 0.4),
      0 0 40px rgba(30, 64, 175, 0.2),
      0 0 80px rgba(30, 64, 175, 0.1);
  }

  .cyan-glow-effect {
    box-shadow: 
      0 0 20px rgba(6, 182, 212, 0.4),
      0 0 40px rgba(6, 182, 212, 0.2),
      0 0 80px rgba(6, 182, 212, 0.1);
  }

  .gold-glow-effect {
    box-shadow: 
      0 0 20px rgba(245, 158, 11, 0.4),
      0 0 40px rgba(245, 158, 11, 0.2),
      0 0 80px rgba(245, 158, 11, 0.1);
  }

  .purple-glow-effect {
    box-shadow: 
      0 0 20px rgba(139, 92, 246, 0.4),
      0 0 40px rgba(139, 92, 246, 0.2),
      0 0 80px rgba(139, 92, 246, 0.1);
  }

  /* Enhanced neon border with animation */
  .neon-border {
    border: 2px solid transparent;
    background: 
      linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.9)) padding-box,
      linear-gradient(135deg, #06B6D4, #8B5CF6, #F59E0B, #10B981) border-box;
    animation: neon-pulse 3s ease-in-out infinite alternate;
    position: relative;
    overflow: hidden;
  }

  .neon-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(6, 182, 212, 0.2), 
      transparent);
    animation: neon-sweep 3s linear infinite;
  }

  /* Enhanced hover scale with 3D effect */
  .hover-scale {
    @apply transition-all duration-500 hover:z-10;
    transform-style: preserve-3d;
  }

  .hover-scale:hover {
    transform: translateY(-10px) rotateX(5deg) scale(1.05);
  }

  /* Enhanced digital grid */
  .digital-grid {
    background-image: 
      linear-gradient(rgba(6, 182, 212, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(6, 182, 212, 0.1) 1px, transparent 1px),
      linear-gradient(rgba(139, 92, 246, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(139, 92, 246, 0.05) 1px, transparent 1px);
    background-size: 20px 20px, 20px 20px, 60px 60px, 60px 60px;
    animation: grid-move 15s linear infinite, grid-pulse 8s ease-in-out infinite;
  }

  /* Enhanced navigation tabs */
  .nav-tab {
    @apply flex flex-col items-center p-4 rounded-xl transition-all duration-500 relative overflow-hidden;
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(6, 182, 212, 0.2);
  }

  .nav-tab.active {
    @apply text-white;
    background: linear-gradient(135deg, #F59E0B 0%, #EAB308 50%, #FCD34D 100%);
    box-shadow: 
      0 0 25px rgba(245, 158, 11, 0.5),
      0 0 50px rgba(245, 158, 11, 0.3);
    border-color: rgba(245, 158, 11, 0.6);
  }

  .nav-tab.inactive {
    @apply text-nav-inactive hover:text-secondary-text;
  }

  .nav-tab.inactive:hover {
    background: rgba(30, 41, 59, 0.9);
    border-color: rgba(6, 182, 212, 0.4);
  }

  /* Enhanced futuristic border */
  .futuristic-border {
    position: relative;
    border-radius: 1rem;
  }

  .futuristic-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    padding: 2px;
    background: linear-gradient(45deg, 
      #06B6D4, #8B5CF6, #F59E0B, #10B981, 
      #06B6D4, #8B5CF6, #F59E0B, #10B981);
    background-size: 400% 400%;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: subtract;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: subtract;
    animation: border-rotate 4s linear infinite;
  }

  /* Enhanced gaming buttons */
  .gaming-button {
    @apply relative overflow-hidden font-bold transition-all duration-500;
    color: white !important;
    background: linear-gradient(135deg, #06B6D4 0%, #8B5CF6 100%);
    border: 2px solid transparent;
    background-clip: padding-box;
    transform-style: preserve-3d;
  }

  /* .gaming-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 
      0 10px 30px rgba(6, 182, 212, 0.4),
      0 0 40px rgba(139, 92, 246, 0.3);
  } */

  .gaming-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(255, 255, 255, 0.3), 
      transparent);
    transition: left 0.6s ease;
  }

  .gaming-button:hover::before {
    left: 100%;
  }

  /* Enhanced cyber button */
  .cyber-button {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%);
    border: 2px solid #06B6D4;
    color: #06B6D4;
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
  }

  .cyber-button:hover {
    background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
    color: #0F172A;
    box-shadow: 
      0 0 30px rgba(6, 182, 212, 0.6),
      0 0 60px rgba(6, 182, 212, 0.4);
    transform: translateY(-5px) scale(1.02);
  }

  .cyber-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(255, 255, 255, 0.2), 
      transparent);
    transition: left 0.7s ease;
  }

  .cyber-button:hover::before {
    left: 100%;
  }

  /* Enhanced social button */
  .social-button {
    @apply text-sm px-6 py-3 hover:shadow-cyan-glow transition-all duration-500 relative overflow-hidden;
    background: linear-gradient(135deg, 
      rgba(6, 182, 212, 0.3) 0%, 
      rgba(139, 92, 246, 0.2) 100%);
    border: 1px solid rgba(6, 182, 212, 0.5);
    backdrop-filter: blur(10px);
  }

  .social-button:hover {
    box-shadow: 
      0 0 25px rgba(6, 182, 212, 0.6),
      0 0 50px rgba(139, 92, 246, 0.4);
    transform: translateY(-3px) scale(1.05);
  }

  /* Gaming panel with enhanced effects */
  .gaming-panel {
    background: linear-gradient(135deg, 
      rgba(30, 41, 59, 0.95) 0%, 
      rgba(51, 65, 85, 0.9) 50%,
      rgba(30, 41, 59, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(6, 182, 212, 0.3);
    box-shadow: 
      0 0 30px rgba(6, 182, 212, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(6, 182, 212, 0.1);
  }

  /* Enhanced hologram effect */
  .hologram-effect {
    background: linear-gradient(135deg, 
      rgba(6, 182, 212, 0.15) 0%, 
      rgba(139, 92, 246, 0.1) 25%,
      rgba(245, 158, 11, 0.05) 50%,
      rgba(139, 92, 246, 0.1) 75%,
      rgba(6, 182, 212, 0.15) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(6, 182, 212, 0.4);
    position: relative;
    overflow: hidden;
  }

  .hologram-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(6, 182, 212, 0.3), 
      rgba(139, 92, 246, 0.2),
      rgba(6, 182, 212, 0.3),
      transparent);
    animation: hologram-scan 4s linear infinite;
  }

  .hologram-effect::after {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(245, 158, 11, 0.2), 
      transparent);
    animation: hologram-scan 6s linear infinite reverse;
  }
}

/* Enhanced custom scrollbar */
::-webkit-scrollbar {
  width: 14px;
}

::-webkit-scrollbar-track {
  background: linear-gradient(180deg, #1E293B 0%, #334155 100%);
  border-radius: 8px;
  border: 1px solid rgba(6, 182, 212, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #06B6D4 0%, #8B5CF6 100%);
  border-radius: 8px;
  border: 2px solid #1E293B;
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #0891B2 0%, #7C3AED 100%);
  box-shadow: 0 0 15px rgba(6, 182, 212, 0.5);
}

/* Enhanced gaming animations */
@keyframes particle-move {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  100% { transform: translateY(-120vh) translateX(150px) rotate(360deg); }
}

@keyframes particle-drift {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  100% { transform: translateY(-110vh) translateX(-100px) rotate(-360deg); }
}

@keyframes neon-pulse {
  0% { 
    box-shadow: 0 0 5px rgba(6, 182, 212, 0.5);
    filter: brightness(1);
  }
  50% {
    box-shadow: 
      0 0 20px rgba(6, 182, 212, 0.8), 
      0 0 30px rgba(139, 92, 246, 0.6),
      0 0 40px rgba(245, 158, 11, 0.4);
    filter: brightness(1.2);
  }
  100% {
    box-shadow: 0 0 5px rgba(6, 182, 212, 0.5);
    filter: brightness(1);
  }
}

@keyframes neon-sweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(20px, 20px); }
}

@keyframes grid-pulse {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.3; }
}

@keyframes border-rotate {
  0% { 
    transform: rotate(0deg);
    background-position: 0% 50%;
  }
  100% { 
    transform: rotate(360deg);
    background-position: 100% 50%;
  }
}

@keyframes hologram-scan {
  0% { 
    left: -100%; 
    opacity: 0;
  }
  10% { 
    opacity: 1;
  }
  90% { 
    opacity: 1;
  }
  100% { 
    left: 100%; 
    opacity: 0;
  }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes fade-in {
  0% { 
    opacity: 0; 
    transform: translateY(20px);
  }
  100% { 
    opacity: 1; 
    transform: translateY(0);
  }
}

@keyframes scale-in {
  0% { 
    opacity: 0; 
    transform: scale(0.8);
  }
  100% { 
    opacity: 1; 
    transform: scale(1);
  }
}

@keyframes slide-in-left {
  0% { 
    opacity: 0; 
    transform: translateX(-30px);
  }
  100% { 
    opacity: 1; 
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  0% { 
    opacity: 0; 
    transform: translateX(30px);
  }
  100% { 
    opacity: 1; 
    transform: translateX(0);
  }
}

/* Enhanced shimmer effect */
.shimmer {
  background: linear-gradient(90deg, 
    rgba(30, 41, 59, 0.8) 0px, 
    rgba(6, 182, 212, 0.3) 100px, 
    rgba(139, 92, 246, 0.2) 150px,
    rgba(30, 41, 59, 0.8) 200px);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced gaming specific animations */
@keyframes coin-spin {
  0% { 
    transform: rotateY(0deg) scale(1); 
    filter: brightness(1);
  }
  50% { 
    transform: rotateY(180deg) scale(1.1); 
    filter: brightness(1.3);
  }
  100% { 
    transform: rotateY(360deg) scale(1); 
    filter: brightness(1);
  }
}

.coin-spin {
  animation: coin-spin 2.5s linear infinite;
}

@keyframes level-up {
  0% { 
    transform: scale(1) rotate(0deg); 
    filter: brightness(1);
  }
  25% { 
    transform: scale(1.15) rotate(-10deg); 
    filter: brightness(1.2);
  }
  50% { 
    transform: scale(1.3) rotate(10deg); 
    filter: brightness(1.4);
  }
  75% { 
    transform: scale(1.15) rotate(-5deg); 
    filter: brightness(1.2);
  }
  100% { 
    transform: scale(1) rotate(0deg); 
    filter: brightness(1);
  }
}

.level-up {
  animation: level-up 1s ease-in-out;
}

@keyframes float-advanced {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1); 
  }
  25% { 
    transform: translateY(-15px) translateX(5px) rotate(3deg) scale(1.02); 
  }
  50% { 
    transform: translateY(-10px) translateX(-5px) rotate(-2deg) scale(1.05); 
  }
  75% { 
    transform: translateY(-20px) translateX(3px) rotate(1deg) scale(1.02); 
  }
}

.float-advanced {
  animation: float-advanced 6s ease-in-out infinite;
}

@keyframes glitch {
  0% { 
    transform: translate(0); 
    filter: hue-rotate(0deg);
  }
  10% { 
    transform: translate(-2px, 2px); 
    filter: hue-rotate(90deg);
  }
  20% { 
    transform: translate(-2px, -2px); 
    filter: hue-rotate(180deg);
  }
  30% { 
    transform: translate(2px, 2px); 
    filter: hue-rotate(270deg);
  }
  40% { 
    transform: translate(2px, -2px); 
    filter: hue-rotate(360deg);
  }
  50% { 
    transform: translate(-1px, 1px); 
    filter: hue-rotate(90deg);
  }
  60% { 
    transform: translate(-1px, -1px); 
    filter: hue-rotate(180deg);
  }
  70% { 
    transform: translate(1px, 1px); 
    filter: hue-rotate(270deg);
  }
  80% { 
    transform: translate(1px, -1px); 
    filter: hue-rotate(360deg);
  }
  90% { 
    transform: translate(-1px, 0px); 
    filter: hue-rotate(90deg);
  }
  100% { 
    transform: translate(0); 
    filter: hue-rotate(0deg);
  }
}

.glitch-effect {
  animation: glitch 0.5s ease-in-out;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 
      0 0 10px rgba(6, 182, 212, 0.3),
      0 0 20px rgba(6, 182, 212, 0.2);
  }
  50% {
    box-shadow: 
      0 0 20px rgba(6, 182, 212, 0.6),
      0 0 40px rgba(6, 182, 212, 0.4),
      0 0 60px rgba(6, 182, 212, 0.2);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced progress bars with gaming colors */
.progress-xp {
  background: linear-gradient(90deg, #8B5CF6 0%, #A855F7 50%, #C084FC 100%);
  box-shadow: 
    0 0 15px rgba(139, 92, 246, 0.6),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
  animation: pulse-glow 3s ease-in-out infinite;
}

.progress-health {
  background: linear-gradient(90deg, #DC2626 0%, #EF4444 50%, #F87171 100%);
  box-shadow: 
    0 0 15px rgba(239, 68, 68, 0.6),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
}

.progress-shield {
  background: linear-gradient(90deg, #2563EB 0%, #3B82F6 50%, #60A5FA 100%);
  box-shadow: 
    0 0 15px rgba(59, 130, 246, 0.6),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
}

.progress-speed {
  background: linear-gradient(90deg, #EAB308 0%, #F59E0B 50%, #FBBF24 100%);
  box-shadow: 
    0 0 15px rgba(245, 158, 11, 0.6),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
}

/* Animation utilities */
.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 1s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 1s ease-out forwards;
}

.animate-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced selection colors */
::selection {
  background: rgba(6, 182, 212, 0.4);
  color: #F8FAFC;
  text-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

::-moz-selection {
  background: rgba(6, 182, 212, 0.4);
  color: #F8FAFC;
  text-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

/* Focus states for accessibility */
:focus-visible {
  outline: 2px solid #06B6D4;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Responsive enhancements */
@media (max-width: 1024px) {
  .digital-grid {
    background-size: 15px 15px, 15px 15px, 45px 45px, 45px 45px;
  }
  
  .gaming-panel {
    backdrop-filter: blur(15px);
  }
}

@media (max-width: 768px) {
  body::before, body::after {
    background-size: 40px 40px, 30px 30px;
  }
  
  .digital-grid {
    background-size: 12px 12px, 12px 12px, 36px 36px, 36px 36px;
  }
  
  .neon-border {
    border-width: 1px;
  }
  
  .gaming-button, .cyber-button {
    transform: none;
  }
  
  .gaming-button:hover, .cyber-button:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

@media (max-width: 640px) {
  .float-advanced {
    animation: float-advanced 4s ease-in-out infinite;
  }
  
  .coin-spin {
    animation: coin-spin 2s linear infinite;
  }
  
  ::-webkit-scrollbar {
    width: 8px;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  body {
    background: 
      radial-gradient(circle at 20% 10%, rgba(30, 64, 175, 0.2) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
      radial-gradient(circle at 90% 90%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #0F172A 0%, #1E293B 25%, #334155 50%, #1E293B 75%, #0F172A 100%);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .neon-border, .gaming-button, .cyber-button {
    border-width: 3px;
  }
  
  .text-secondary-text {
    color: #E2E8F0;
  }
  
  .text-muted-text {
    color: #CBD5E1;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .float-advanced, .coin-spin, .level-up, .glitch-effect {
    animation: none;
  }
  
  body::before, body::after {
    animation: none;
  }
}