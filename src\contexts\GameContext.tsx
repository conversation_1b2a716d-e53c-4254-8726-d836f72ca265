import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { Game, UserStats } from "@/types/api";

// Auth Token storage key
const AUTH_TOKEN_STORAGE_KEY = "jq_auth_token";

interface GameContextType {
  // Auth Token management
  authToken: string | null;
  setAuthToken: (token: string) => void;
  clearAuthToken: () => void;

  // Account Connect modal
  openAccountConnectModal: () => void;

  // Selected game management
  selectedGame: Game | null;
  setSelectedGame: (game: Game | null) => void;

  // User stats for current game
  currentUserStats: UserStats | null;
  setCurrentUserStats: (stats: UserStats | null) => void;
  gameStats: Record<string, UserStats> | null;
  setGameStats: (stats: Record<string, UserStats> | null) => void;

  // Modal state for Account Connect input
  showAccountConnectModal: boolean;
  setShowAccountConnectModal: (show: boolean) => void;

  // Helper functions
  isGameSelected: boolean;
  hasAuthToken: boolean;
}

const GameContext = createContext<GameContextType | undefined>(undefined);

export const useGame = () => {
  const context = useContext(GameContext);
  if (context === undefined) {
    throw new Error("useGame must be used within a GameProvider");
  }
  return context;
};

interface GameProviderProps {
  children: ReactNode;
}

export const GameProvider: React.FC<GameProviderProps> = ({ children }) => {
  const [authToken, setAuthTokenState] = useState<string | null>(null);
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [currentUserStats, setCurrentUserStats] = useState<UserStats | null>(
    null
  );
  const [gameStats, setGameStats] = useState<Record<string, UserStats> | null>(null);
  const [showAccountConnectModal, setShowAccountConnectModal] = useState(false);

  // Load Auth Token from localStorage on mount
  useEffect(() => {
    const storedAuthToken = localStorage.getItem(AUTH_TOKEN_STORAGE_KEY);
    if (storedAuthToken) {
      setAuthTokenState(storedAuthToken);
    }
  }, []);

  const openAccountConnectModal = () => {
    if (!hasAuthToken) setShowAccountConnectModal(true);
  };

  // Set Auth Token and persist to localStorage
  const setAuthToken = (token: string) => {
    setAuthTokenState(token);
    localStorage.setItem(AUTH_TOKEN_STORAGE_KEY, token);
  };

  // Clear Auth Token from state and localStorage
  const clearAuthToken = () => {
    setAuthTokenState(null);
    localStorage.removeItem(AUTH_TOKEN_STORAGE_KEY);
    setCurrentUserStats(null); // Clear stats when clearing token
    setGameStats(null); // Clear game stats when clearing token
  };

  // Computed values
  const isGameSelected = selectedGame !== null;
  const hasAuthToken = authToken !== null && authToken.trim() !== "";

  const value: GameContextType = {
    // Auth Token management
    authToken,
    setAuthToken,
    clearAuthToken,

    // Account Connect modal
    openAccountConnectModal,

    // Selected game management
    selectedGame,
    setSelectedGame,

    // User stats for current game
    currentUserStats,
    setCurrentUserStats,
    gameStats,
    setGameStats,

    // Modal state
    showAccountConnectModal,
    setShowAccountConnectModal,

    // Helper values
    isGameSelected,
    hasAuthToken,
  };

  return <GameContext.Provider value={value}>{children}</GameContext.Provider>;
};
