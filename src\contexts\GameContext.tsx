import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { Game, UserStats } from "@/types/api";

// Account Token storage key
const ACCOUNT_TOKEN_STORAGE_KEY = "jq_account_token";
const PASSWORD_STORAGE_KEY = "jq_password";

interface GameContextType {
  // Account Token management
  accountToken: string | null;
  password: string | null;
  setAccountCredentials: (token: string, password: string) => void;
  clearAccountCredentials: () => void;

  // Account Token modal
  openAccountConnectModal: () => void;

  // Selected game management
  selectedGame: Game | null;
  setSelectedGame: (game: Game | null) => void;

  // User stats for current game
  currentUserStats: UserStats | null;
  setCurrentUserStats: (stats: UserStats | null) => void;

  // Modal state for Account Token input
  showAccountConnectModal: boolean;
  setShowAccountConnectModal: (show: boolean) => void;

  // Helper functions
  isGameSelected: boolean;
  hasAccountToken: boolean;
}

const GameContext = createContext<GameContextType | undefined>(undefined);

export const useGame = () => {
  const context = useContext(GameContext);
  if (context === undefined) {
    throw new Error("useGame must be used within a GameProvider");
  }
  return context;
};

interface GameProviderProps {
  children: ReactNode;
}

export const GameProvider: React.FC<GameProviderProps> = ({ children }) => {
  const [accountToken, setAccountTokenState] = useState<string | null>(null);
  const [password, setPasswordState] = useState<string | null>(null);
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [currentUserStats, setCurrentUserStats] = useState<UserStats | null>(
    null
  );
  const [showAccountConnectModal, setShowAccountConnectModal] = useState(false);

  // Load Account Token and Password from localStorage on mount
  useEffect(() => {
    const storedAccountToken = localStorage.getItem(ACCOUNT_TOKEN_STORAGE_KEY);
    const storedPassword = localStorage.getItem(PASSWORD_STORAGE_KEY);
    if (storedAccountToken) {
      setAccountTokenState(storedAccountToken);
    }
    if (storedPassword) {
      setPasswordState(storedPassword);
    }
  }, []);

  const openAccountConnectModal = () => {
    if (!hasAccountToken) setShowAccountConnectModal(true);
  };

  // Set Account Credentials and persist to localStorage
  const setAccountCredentials = (token: string, pass: string) => {
    setAccountTokenState(token);
    setPasswordState(pass);
    localStorage.setItem(ACCOUNT_TOKEN_STORAGE_KEY, token);
    localStorage.setItem(PASSWORD_STORAGE_KEY, pass);
  };

  // Clear Account Credentials from state and localStorage
  const clearAccountCredentials = () => {
    setAccountTokenState(null);
    setPasswordState(null);
    localStorage.removeItem(ACCOUNT_TOKEN_STORAGE_KEY);
    localStorage.removeItem(PASSWORD_STORAGE_KEY);
    setCurrentUserStats(null); // Clear stats when clearing credentials
  };

  // Computed values
  const isGameSelected = selectedGame !== null;
  const hasAccountToken = accountToken !== null && accountToken.trim() !== "";

  const value: GameContextType = {
    // Account Token management
    accountToken,
    password,
    setAccountCredentials,
    clearAccountCredentials,

    // Account Token modal
    openAccountConnectModal,

    // Selected game management
    selectedGame,
    setSelectedGame,

    // User stats for current game
    currentUserStats,
    setCurrentUserStats,

    // Modal state
    showAccountConnectModal,
    setShowAccountConnectModal,

    // Helper values
    isGameSelected,
    hasAccountToken,
  };

  return <GameContext.Provider value={value}>{children}</GameContext.Provider>;
};
