# Backend Controller Issues to Fix

## 1. connectAccount Controller Issues

```javascript
// Issue 1: userData is an object, not an array
const authToken = generateExternalAccessToken({
  userId: userData[0]._id, // ❌ Should be: userData._id
});

const isToken = await ExternalToken.create({
  user_id: userData[0]._id, // ❌ Should be: userData._id
  token: authToken,
});

// Issue 2: Missing game_id variable
let userStats = await JP_UserStats.findOne({
  user_id: userData._id,
  game_id: game_id, // ❌ game_id is not defined
});

// Issue 3: userStats.reduce() on single object
const gameStats = userStats.reduce((acc, game) => {
  // ❌ userStats is a single object, not an array
  // Should use JP_UserStats.find() to get array
});
```

## 2. connectAccountWithAuth Controller Issues

```javascript
// Issue 1: Same userData array access issue
const authToken = generateExternalAccessToken({
  userId: userData[0]._id, // ❌ Should be: userData._id
});

// Issue 2: Wrong update field name
const isToken = await ExternalToken.updateOne(
  { user_id: userData[0]._id, token: oldToken },
  { $set: { authToken } } // ❌ Should be: { $set: { token: authToken } }
).exec();
```

## 3. Suggested Fixed Controllers

### Fixed connectAccount:
```javascript
const connectAccount = generateController(
  async (request, response, raiseException) => {
    const { token, password } = request.body;

    const userId = extractUserIdFromToken(token, password);

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      { _id: 1 }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    const authToken = generateExternalAccessToken({
      userId: userData._id, // Fixed: removed [0]
    });

    const isToken = await ExternalToken.create({
      user_id: userData._id, // Fixed: removed [0]
      token: authToken,
    });

    if (!isToken) {
      return raiseException(500, "Token creation failed");
    }

    // Get all user stats for all games
    let userStats = await JP_UserStats.find({
      user_id: userData._id,
    });

    const gameStats = userStats.reduce((acc, game) => {
      acc[game.game_id] = {
        best_score: game.best_score,
        games_played: game.games_played,
        total_score: game.total_score,
      };
      return acc;
    }, {});

    return {
      message: "Account connected successfully",
      payload: {
        user_data: userData,
        auth_token: authToken,
        game_stats: gameStats,
      },
    };
  }
);
```

### Fixed connectAccountWithAuth:
```javascript
const connectAccountWithAuth = generateController(
  async (request, response, raiseException) => {
    const { userId, oldToken } = request.user;

    const userData = await User.findOne(
      { _id: userId, is_deleted: false },
      { _id: 1 }
    );

    if (!userData) {
      return raiseException(404, "User not found");
    }

    const authToken = generateExternalAccessToken({
      userId: userData._id, // Fixed: removed [0]
    });

    const isToken = await ExternalToken.updateOne(
      { user_id: userData._id, token: oldToken }, // Fixed: removed [0]
      { $set: { token: authToken } } // Fixed: field name
    ).exec();

    if (!isToken.matchedCount) {
      return raiseException(500, "Token update failed");
    }

    // Get all user stats for all games
    let userStats = await JP_UserStats.find({
      user_id: userData._id,
    });

    const gameStats = userStats.reduce((acc, game) => {
      acc[game.game_id] = {
        best_score: game.best_score,
        games_played: game.games_played,
        total_score: game.total_score,
      };
      return acc;
    }, {});

    return {
      message: "Account connected successfully",
      payload: {
        user_data: userData,
        auth_token: authToken,
        game_stats: gameStats,
      },
    };
  }
);
```

## 4. Auth Middleware Fix

The auth middleware also has an issue:

```javascript
// Issue: Wrong model name
await Token.deleteOne({ token }); // ❌ Should be: ExternalToken.deleteOne({ token });
```

Fixed version:
```javascript
} catch (err) {
  // If the token is invalid or expired, delete it from the database
  await ExternalToken.deleteOne({ token }); // Fixed: use correct model
  return res.status(403).json({
    message: "Token expired or invalid",
    success: false,
  });
}
```
