import { apiClient } from '@/lib/api-client';
import {
  API_ENDPOINTS,
  GamesResponse,
  ConnectAccountRequest,
  ConnectAccountResponse,
  ConnectAccountWithAuthResponse,
  UpdateScoreRequest,
  UpdateScoreResponse,
  UserStats,
} from '@/types/api';

// Game API Service
export class GameApiService {
  /**
   * Fetch all games
   */
  static async getGames() {
    try {
      const response = await apiClient.get<GamesResponse>(API_ENDPOINTS.GAMES);
      return response.payload.games;
    } catch (error) {
      console.error('Error fetching games:', error);
      throw error;
    }
  }

  /**
   * Connect account with token and password (initial connection)
   */
  static async connectAccount(token: string, password: string) {
    try {
      const requestData: ConnectAccountRequest = {
        token,
        password,
      };

      const response = await apiClient.post<ConnectAccountResponse>(
        API_ENDPOINTS.CONNECT_ACCOUNT,
        requestData
      );

      return response.payload;
    } catch (error) {
      console.error('Error connecting account:', error);
      throw error;
    }
  }

  /**
   * Connect account with existing auth token (automatic connection)
   */
  static async connectAccountWithAuth(authToken: string) {
    try {
      const response = await apiClient.post<ConnectAccountWithAuthResponse>(
        API_ENDPOINTS.CONNECT_ACCOUNT_WITH_AUTH,
        undefined, // No body needed
        authToken
      );

      return response.payload;
    } catch (error) {
      console.error('Error connecting account with auth:', error);
      throw error;
    }
  }

  /**
   * Update user score for a specific game
   */
  static async updateScore(gameId: string, score: number, authToken: string) {
    try {
      const requestData: UpdateScoreRequest = {
        game_id: gameId,
        score: score,
      };

      const response = await apiClient.patch<UpdateScoreResponse>(
        API_ENDPOINTS.UPDATE_SCORE,
        requestData,
        authToken
      );

      return response.payload;
    } catch (error) {
      console.error('Error updating score:', error);
      throw error;
    }
  }
}

// Export individual functions for easier importing
export const { getGames, connectAccount, connectAccountWithAuth, updateScore } = GameApiService;
