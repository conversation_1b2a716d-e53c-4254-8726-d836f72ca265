/* Enhanced JargonQuest Home Styles */

.heroSection {
  position: relative;
  overflow: hidden;
}

/* Enhanced glow effects */
.hero-glow {
  position: relative;
}

.hero-glow::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300%;
  height: 300%;
  background: radial-gradient(
    circle, 
    rgba(6, 182, 212, 0.15) 0%, 
    rgba(139, 92, 246, 0.1) 30%,
    rgba(245, 158, 11, 0.05) 60%,
    transparent 80%
  );
  transform: translate(-50%, -50%);
  animation: hero-pulse 6s ease-in-out infinite;
  pointer-events: none;
}

/* Enhanced text effects */
.gradient-text {
  background: linear-gradient(135deg, #06B6D4 0%, #8B5CF6 50%, #3B82F6 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 4s ease-in-out infinite;
  position: relative;
}

.gold-text {
  background: linear-gradient(135deg, #F59E0B 0%, #EAB308 30%, #FCD34D 60%, #F59E0B 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gold-sparkle 3s ease-in-out infinite;
  position: relative;
}

/* Enhanced metric cards */
.metric-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.8) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 1.5rem;
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fade-in-up 1s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(6, 182, 212, 0.1), 
    transparent);
  transition: left 0.8s ease;
}

.metric-card:hover::before {
  left: 100%;
}

.metric-card:hover {
  transform: translateY(-10px) scale(1.05);
  border-color: rgba(6, 182, 212, 0.6);
  box-shadow: 
    0 20px 40px rgba(6, 182, 212, 0.2),
    0 0 30px rgba(6, 182, 212, 0.3);
}

.metric-number {
  font-weight: 900;
  font-family: 'JetBrains Mono', monospace;
  background: linear-gradient(135deg, #06B6D4 0%, #8B5CF6 50%, #F59E0B 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
  animation: number-glow 2s ease-in-out infinite alternate;
}

.metric-label {
  color: #CBD5E1;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.metric-card:hover .metric-label {
  color: #F8FAFC;
}

/* Enhanced game cards */
.game-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 2rem;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fade-in-up 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}

.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.1) 0%, 
    rgba(139, 92, 246, 0.1) 50%, 
    rgba(245, 158, 11, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  border-radius: inherit;
}

.game-card:hover::before {
  opacity: 1;
}

.game-card:hover {
  transform: translateY(-15px) rotateX(5deg) rotateY(5deg);
  border-color: rgba(6, 182, 212, 0.5);
  box-shadow: 
    0 25px 50px rgba(6, 182, 212, 0.2),
    0 0 40px rgba(139, 92, 246, 0.1),
    0 0 20px rgba(245, 158, 11, 0.1);
}

/* Blockchain badges */
.blockchain-badge {
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.bsc-badge {
  background: linear-gradient(135deg, #F0B90B 0%, #F59E0B 100%);
  color: #0F172A;
  box-shadow: 0 0 15px rgba(240, 185, 11, 0.4);
}

.sol-badge {
  background: linear-gradient(135deg, #9945FF 0%, #8B5CF6 100%);
  color: #FFFFFF;
  box-shadow: 0 0 15px rgba(153, 69, 255, 0.4);
}

.eth-badge {
  background: linear-gradient(135deg, #627EEA 0%, #3B82F6 100%);
  color: #FFFFFF;
  box-shadow: 0 0 15px rgba(98, 126, 234, 0.4);
}

/* Enhanced animations */
@keyframes hero-pulse {
  0%, 100% { 
    opacity: 0.3; 
    transform: translate(-50%, -50%) scale(1) rotate(0deg); 
  }
  50% { 
    opacity: 0.6; 
    transform: translate(-50%, -50%) scale(1.2) rotate(180deg); 
  }
}

@keyframes gradient-shift {
  0%, 100% { 
    background-position: 0% 50%; 
    filter: hue-rotate(0deg);
  }
  25% { 
    background-position: 100% 50%; 
    filter: hue-rotate(90deg);
  }
  50% { 
    background-position: 200% 50%; 
    filter: hue-rotate(180deg);
  }
  75% { 
    background-position: 100% 50%; 
    filter: hue-rotate(270deg);
  }
}

@keyframes gold-sparkle {
  0%, 100% { 
    background-position: 0% 50%; 
    filter: brightness(1) saturate(1) hue-rotate(0deg);
  }
  25% { 
    background-position: 100% 50%; 
    filter: brightness(1.2) saturate(1.2) hue-rotate(15deg);
  }
  50% { 
    background-position: 200% 50%; 
    filter: brightness(1.4) saturate(1.4) hue-rotate(30deg);
  }
  75% { 
    background-position: 300% 50%; 
    filter: brightness(1.2) saturate(1.2) hue-rotate(15deg);
  }
}

@keyframes number-glow {
  0% { 
    text-shadow: 
      0 0 10px rgba(6, 182, 212, 0.3),
      0 0 20px rgba(6, 182, 212, 0.2),
      0 0 30px rgba(6, 182, 212, 0.1);
  }
  100% { 
    text-shadow: 
      0 0 20px rgba(139, 92, 246, 0.4),
      0 0 30px rgba(139, 92, 246, 0.3),
      0 0 40px rgba(139, 92, 246, 0.2);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes particle-drift {
  0% { 
    transform: translateY(0px) translateX(0px) rotate(0deg); 
    opacity: 0;
  }
  10% { 
    opacity: 1;
  }
  90% { 
    opacity: 1;
  }
  100% { 
    transform: translateY(-100vh) translateX(200px) rotate(360deg); 
    opacity: 0;
  }
}

@keyframes hologram-scan {
  0% { 
    left: -100%; 
    opacity: 0;
  }
  50% { 
    opacity: 1;
  }
  100% { 
    left: 100%; 
    opacity: 0;
  }
}

@keyframes border-glow {
  0%, 100% {
    box-shadow: 
      0 0 5px rgba(6, 182, 212, 0.3),
      inset 0 0 5px rgba(6, 182, 212, 0.1);
  }
  50% {
    box-shadow: 
      0 0 20px rgba(6, 182, 212, 0.6),
      0 0 30px rgba(139, 92, 246, 0.4),
      inset 0 0 10px rgba(6, 182, 212, 0.2);
  }
}

/* Enhanced floating animations */
@keyframes float-advanced {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1); 
  }
  25% { 
    transform: translateY(-15px) translateX(5px) rotate(2deg) scale(1.05); 
  }
  50% { 
    transform: translateY(-10px) translateX(-5px) rotate(-1deg) scale(1.1); 
  }
  75% { 
    transform: translateY(-20px) translateX(3px) rotate(1deg) scale(1.05); 
  }
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .metric-card {
    padding: 1.5rem;
  }
  
  .game-card {
    padding: 1.5rem;
  }
  
  .gradient-text,
  .gold-text {
    background-size: 100% 100%;
  }
  
  .hero-glow::before {
    width: 200%;
    height: 200%;
  }
}

@media (max-width: 640px) {
  .metric-card {
    padding: 1rem;
  }
  
  .game-card {
    padding: 1rem;
  }
  
  .metric-number {
    font-size: 2rem;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, 
    rgba(30, 41, 59, 0.8) 0px, 
    rgba(6, 182, 212, 0.2) 100px, 
    rgba(30, 41, 59, 0.8) 200px);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* Enhanced hover states */
.hover-glow:hover {
  animation: border-glow 2s ease-in-out infinite;
}

/* Custom selection colors */
::selection {
  background: rgba(6, 182, 212, 0.3);
  color: #F8FAFC;
}

::-moz-selection {
  background: rgba(6, 182, 212, 0.3);
  color: #F8FAFC;
}