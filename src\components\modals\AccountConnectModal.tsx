import React, { useState, useEffect } from "react";
import { X, User, AlertCircle, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGame } from "@/contexts/GameContext";

interface AccountConnectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (accountToken: string, password: string) => void;
}

export const AccountConnectModal: React.FC<AccountConnectModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [accountToken, setAccountToken] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const { accountToken: storedAccountToken } = useGame();

  // Pre-fill with stored Account Token if available
  useEffect(() => {
    if (storedAccountToken) {
      setAccountToken(storedAccountToken);
    }
  }, [storedAccountToken]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setError("");
      setIsValidating(false);
      setPassword(""); // Clear password when modal closes
    }
  }, [isOpen]);

  // Function to get validation error message
  const getValidationError = (token: string, pass: string): string => {
    const trimmedToken = token.trim();
    const trimmedPassword = pass.trim();

    if (!trimmedToken) {
      return "Account Token is required";
    }

    if (!trimmedPassword) {
      return "Password is required";
    }

    if (trimmedToken.length < 5) {
      return "Account Token must be at least 5 characters long";
    }

    if (trimmedPassword.length < 6) {
      return "Password must be at least 6 characters long";
    }

    // Check if it's a valid format (can be numeric or username)
    const isNumeric = /^\d+$/.test(trimmedToken);
    const isUsername = /^[a-zA-Z0-9_]{5,32}$/.test(trimmedToken);

    if (!isNumeric && !isUsername) {
      return "Invalid Account Token format. Use your numeric ID or username.";
    }

    return "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = getValidationError(accountToken, password);
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsValidating(true);

    try {
      // Here you could add additional validation by calling API
      // For now, we'll just validate the format
      await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API call

      onSubmit(accountToken.trim(), password.trim());
      onClose();
    } catch (error) {
      setError("Failed to validate credentials. Please try again.");
    } finally {
      setIsValidating(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAccountToken(value);

    // Clear error when user starts typing
    if (error) {
      setError("");
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);

    // Clear error when user starts typing
    if (error) {
      setError("");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-card-dark border border-border-light rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-accent-cyan/10 rounded-lg">
              <User className="h-5 w-5 text-accent-cyan" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-primary-text">
                Connect Account
              </h2>
              <p className="text-sm text-secondary-text">
                Required to track your game progress
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-secondary-text hover:text-primary-text"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="account-token" className="text-primary-text">
              Account Token
            </Label>
            <Input
              id="account-token"
              type="text"
              value={accountToken}
              onChange={handleInputChange}
              placeholder="Enter your account token"
              className="bg-surface-dark border-border-light text-primary-text placeholder:text-muted-text"
              disabled={isValidating}
            />

            {/* Error message */}
            {error && (
              <div className="flex items-center space-x-2 text-sm text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            )}

            {/* Success message */}
            {!error &&
              accountToken &&
              password &&
              getValidationError(accountToken, password) === "" && (
                <div className="flex items-center space-x-2 text-sm text-green-400">
                  <CheckCircle className="h-4 w-4" />
                  <span>Valid credentials format</span>
                </div>
              )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-primary-text">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={handlePasswordChange}
              placeholder="Enter your password"
              className="bg-surface-dark border-border-light text-primary-text placeholder:text-muted-text"
              disabled={isValidating}
            />
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isValidating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-accent-cyan to-xp-purple"
              disabled={isValidating || !accountToken.trim() || !password.trim()}
            >
              {isValidating ? "Validating..." : "Continue"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
