import React, { useState, useEffect } from "react";
import { X, User, AlertCircle, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGame } from "@/contexts/GameContext";
import { useConnectAccount } from "@/hooks/use-game-api";
import { toast } from "@/hooks/use-toast";

interface AccountConnectModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AccountConnectModal: React.FC<AccountConnectModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [accountToken, setAccountToken] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const { setAuthToken, setGameStats } = useGame();
  const connectAccountMutation = useConnectAccount();

  const isValidating = connectAccountMutation.isPending;

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setError("");
      setPassword(""); // Clear password when modal closes
    }
  }, [isOpen]);

  // Helper function to check if password meets complexity requirements
  const isPasswordValid = (pass: string): boolean => {
    const passwordPattern =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return pass.length >= 8 && pass.length <= 30 && passwordPattern.test(pass);
  };

  // Function to get validation error message
  const getValidationError = (token: string, pass: string): string => {
    const trimmedToken = token.trim();
    const trimmedPassword = pass.trim();

    if (!trimmedToken) {
      return "Account Token is required";
    }

    if (!trimmedPassword) {
      return "Password is required";
    }

    // Password length validation
    if (trimmedPassword.length < 8) {
      return "Password must be at least 8 characters";
    }

    if (trimmedPassword.length > 30) {
      return "Password must not exceed 30 characters";
    }

    // Password complexity validation - matches backend Joi pattern
    const passwordPattern =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

    if (!passwordPattern.test(trimmedPassword)) {
      return "Password must have at least 1 uppercase, 1 lowercase, 1 number, and 1 special character (@$!%*?&)";
    }

    return "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = getValidationError(accountToken, password);
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      const result = await connectAccountMutation.mutateAsync({
        token: accountToken.trim(),
        password: password.trim(),
      });

      // Store the auth token and game stats
      setAuthToken(result.payload.auth_token);
      setGameStats(result.payload.game_stats);

      toast({
        title: "Account Connected",
        description: "Your progress will now be tracked!",
      });

      onClose();
    } catch (error: any) {
      setError(
        error?.message || "Failed to connect account. Please try again."
      );
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAccountToken(value);

    // Clear error when user starts typing
    if (error) {
      setError("");
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);

    // Clear error when user starts typing
    if (error) {
      setError("");
    }

    // Real-time validation for password length
    if (value.length > 30) {
      setError("Password cannot exceed 30 characters");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-card-dark border border-border-light rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-accent-cyan/10 rounded-lg">
              <User className="h-5 w-5 text-accent-cyan" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-primary-text">
                Connect Account
              </h2>
              <p className="text-sm text-secondary-text">
                Required to track your game progress
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-secondary-text hover:text-primary-text"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="account-token" className="text-primary-text">
              Account Token
            </Label>
            <Input
              id="account-token"
              type="text"
              value={accountToken}
              onChange={handleInputChange}
              placeholder="Enter your account token"
              className={`bg-surface-dark text-primary-text placeholder:text-muted-text ${
                accountToken.trim().length > 0
                  ? "border-green-500 focus:border-green-500"
                  : "border-border-light"
              }`}
              disabled={isValidating}
            />

            {/* Error message */}
            {error && (
              <div className="flex items-center space-x-2 text-sm text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            )}

            {/* Success message */}
            {!error &&
              accountToken &&
              password &&
              getValidationError(accountToken, password) === "" && (
                <div className="flex items-center space-x-2 text-sm text-green-400">
                  <CheckCircle className="h-4 w-4" />
                  <span>Valid credentials format</span>
                </div>
              )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-primary-text">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={handlePasswordChange}
              placeholder="Enter your password"
              className={`bg-surface-dark text-primary-text placeholder:text-muted-text ${
                isPasswordValid(password)
                  ? "border-green-500 focus:border-green-500"
                  : password.length > 0 && !isPasswordValid(password)
                  ? "border-red-500 focus:border-red-500"
                  : "border-border-light"
              }`}
              disabled={isValidating}
              maxLength={30}
            />
            <div className="space-y-1">
              <p className="text-xs text-muted-text">
                8-30 characters with uppercase, lowercase, number, and special
                character (@$!%*?&)
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isValidating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-accent-cyan to-xp-purple"
              disabled={
                isValidating || !accountToken.trim() || !password.trim()
              }
            >
              {isValidating ? "Validating..." : "Continue"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
