/* Enhanced JargonQuest Games Styles */

.gamesHeader {
  position: relative;
  overflow: hidden;
}

/* Enhanced game cards */
.game-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 1.5rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1);
  animation: fade-in-up 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.game-card:hover {
  border-color: rgba(6, 182, 212, 0.5);
  box-shadow: 
    0 20px 40px rgba(6, 182, 212, 0.2),
    0 0 30px rgba(139, 92, 246, 0.1),
    0 0 20px rgba(245, 158, 11, 0.1);
  transform: translateY(-10px) scale(1.02);
}

.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.1) 0%, 
    rgba(139, 92, 246, 0.05) 50%, 
    rgba(245, 158, 11, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  border-radius: inherit;
  pointer-events: none;
}

.game-card:hover::before {
  opacity: 1;
}

.game-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(6, 182, 212, 0.1), 
    transparent);
  transition: left 0.6s ease;
  pointer-events: none;
}