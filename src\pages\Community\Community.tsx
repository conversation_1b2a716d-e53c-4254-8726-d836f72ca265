import { useState } from 'react';
import { Send, Hash, Users, MessageCircle, Smile, Pin, Search, Filter, Crown, Zap, Star, Trophy, Shield, Globe, Heart, Sparkles } from 'lucide-react';
import { useWallet } from '@/contexts/WalletContext';

const Community = () => {
  const [activeChannel, setActiveChannel] = useState('general');
  const [message, setMessage] = useState('');
  const { isConnected, address, connectWallet, disconnectWallet } = useWallet();

  const channels = [
    { id: 'general', name: '# general', members: 156789, unread: 0, icon: '💬', category: 'Text Channels' },
    { id: 'gaming', name: '# gaming', members: 89456, unread: 3, icon: '🎮', category: 'Text Channels' },
    { id: 'trading', name: '# trading', members: 67543, unread: 0, icon: '📈', category: 'Text Channels' },
    { id: 'nft', name: '# nft-discussion', members: 45321, unread: 12, icon: '🖼️', category: 'Text Channels' },
    { id: 'support', name: '# support', members: 34567, unread: 0, icon: '🛟', category: 'Text Channels' },
    { id: 'announcements', name: '# announcements', members: 156789, unread: 1, icon: '📢', category: 'Text Channels' },
    { id: 'tournaments', name: '# tournaments', members: 78945, unread: 5, icon: '🏆', category: 'Gaming' },
    { id: 'leaderboards', name: '# leaderboards', members: 92341, unread: 0, icon: '👑', category: 'Gaming' },
  ];

  const messages = [
    {
      id: 1,
      user: 'CryptoGamer123',
      avatar: '🎮',
      message: 'Just earned 250 JQ in Crypto Runner Elite! This game is absolutely incredible! The new power-up system makes it so much more engaging 😄',
      timestamp: '2 mins ago',
      reactions: [{ emoji: '🎉', count: 12 }, { emoji: '🔥', count: 8 }, { emoji: '🚀', count: 5 }],
      pinned: false,
      level: 42,
      badges: ['🏆', '⚡'],
    },
    {
      id: 2,
      user: 'BlockchainPro',
      avatar: '🚀',
      message: 'Has anyone tried the new DeFi Defense Wars game? The strategy element is incredible! I love how it combines tower defense with yield farming mechanics.',
      timestamp: '5 mins ago',
      reactions: [{ emoji: '🤔', count: 5 }, { emoji: '👍', count: 15 }, { emoji: '💡', count: 7 }],
      pinned: false,
      level: 38,
      badges: ['💎', '🧠'],
    },
    {
      id: 3,
      user: 'JQ Gaming Team',
      avatar: '👑',
      message: '🎉 MAJOR ANNOUNCEMENT: Epic Tournament Series starting next week! Total prize pool: 10,000 BNB across multiple games! Registration opens tomorrow at 12:00 UTC. Prepare for legendary battles! 🏆✨',
      timestamp: '1 hour ago',
      reactions: [{ emoji: '🎉', count: 234 }, { emoji: '🔥', count: 156 }, { emoji: '💰', count: 89 }, { emoji: '🚀', count: 67 }],
      pinned: true,
      staff: true,
      level: 'MAX',
      badges: ['👑', '⚡', '🌟'],
    },
    {
      id: 4,
      user: 'NFTHunter',
      avatar: '🏆',
      message: 'Check out my latest NFT collection from completing all Puzzle Matrix challenges! The artwork is absolutely stunning 🧩✨ Anyone else working on the legendary difficulty?',
      timestamp: '2 hours ago',
      reactions: [{ emoji: '✨', count: 23 }, { emoji: '🏆', count: 45 }, { emoji: '🎨', count: 12 }],
      pinned: false,
      level: 35,
      badges: ['🎯', '🎨'],
    },
    {
      id: 5,
      user: 'DeFiMaster',
      avatar: '💎',
      message: 'Trading wisdom: Always DYOR before investing in any project. The gaming tokens are looking incredibly bullish though! JQ ecosystem is revolutionizing GameFi 📈💎',
      timestamp: '3 hours ago',
      reactions: [{ emoji: '📈', count: 67 }, { emoji: '💯', count: 34 }, { emoji: '🧠', count: 28 }],
      pinned: false,
      level: 47,
      badges: ['💎', '📈', '🧠'],
    },
    {
      id: 6,
      user: 'MetaverseMike',
      avatar: '🌐',
      message: 'The community events this week were amazing! Shoutout to everyone who participated in the speed run competitions. Next week\'s guild battles are going to be epic! 🔥',
      timestamp: '4 hours ago',
      reactions: [{ emoji: '🔥', count: 45 }, { emoji: '⚔️', count: 23 }, { emoji: '🎊', count: 18 }],
      pinned: false,
      level: 29,
      badges: ['🌐', '⚔️'],
    },
  ];

  const onlineUsers = [
    { name: 'CryptoGamer123', avatar: '🎮', status: 'Playing Crypto Runner Elite', level: 42, role: 'Legend' },
    { name: 'BlockchainPro', avatar: '🚀', status: 'In Tournament Lobby', level: 38, role: 'Pro' },
    { name: 'NFTHunter', avatar: '🏆', status: 'Trading NFTs', level: 35, role: 'Collector' },
    { name: 'DeFiMaster', avatar: '💎', status: 'Analyzing Markets', level: 47, role: 'Elite' },
    { name: 'Web3Warrior', avatar: '⚔️', status: 'In Epic Battle', level: 31, role: 'Warrior' },
    { name: 'MetaverseMike', avatar: '🌐', status: 'Guild Planning', level: 29, role: 'Explorer' },
    { name: 'QuantumQueen', avatar: '👸', status: 'Streaming Gameplay', level: 44, role: 'Streamer' },
    { name: 'CyberSamurai', avatar: '🥷', status: 'Speedrunning', level: 39, role: 'Speedster' },
  ];

  const communityStats = {
    totalMembers: 156789,
    onlineNow: 8,
    messagesLastHour: 1247,
    activeChannels: channels.length,
  };

  const handleSendMessage = () => {
    if (message.trim() && isConnected) {
      console.log('Sending message:', message);
      setMessage('');
    }
  };

  const handleReaction = (messageId, emoji) => {
    console.log('Adding reaction:', emoji, 'to message:', messageId);
  };

  const getChannelsByCategory = (category) => {
    return channels.filter(channel => channel.category === category);
  };

  const getRoleColor = (role) => {
    const colors = {
      'Legend': 'text-orange-400',
      'Elite': 'text-purple-400',
      'Pro': 'text-cyan-400',
      'Warrior': 'text-orange-500',
      'Collector': 'text-green-400',
      'Explorer': 'text-blue-400',
      'Streamer': 'text-purple-500',
      'Speedster': 'text-red-400',
    };
    return colors[role] || 'text-gray-400';
  };

  return (
    <div className="min-h-screen bg-slate-950">
      <div className="flex h-screen pt-16">
        {/* Sidebar */}
        <div className="w-80 bg-slate-900/95 border-r border-slate-800 flex flex-col">
          {/* Server Header */}
          <div className="p-6 border-b border-slate-800 bg-slate-900/80">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                <Crown className="h-6 w-6 text-orange-400" />
              </div>
              <div>
                <h2 className="font-bold text-xl text-white">
                  JQ Gaming Universe
                </h2>
                <p className="text-sm text-gray-400">Epic Community Hub</p>
              </div>
            </div>
            
            {/* Community Stats */}
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-slate-800/60 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-cyan-400">{communityStats.totalMembers.toLocaleString()}</div>
                <div className="text-xs text-gray-500">Total Legends</div>
              </div>
              <div className="bg-slate-800/60 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-green-400">{communityStats.onlineNow}</div>
                <div className="text-xs text-gray-500">Online Now</div>
              </div>
            </div>
          </div>

          {/* Channels */}
          <div className="flex-1 overflow-y-auto p-4">
            {/* Text Channels */}
            <div className="space-y-1 mb-6">
              <h3 className="text-xs font-bold text-orange-400 uppercase tracking-wide mb-3 flex items-center">
                <Hash className="w-3 h-3 mr-1" />
                Text Channels
              </h3>
              {getChannelsByCategory('Text Channels').map((channel) => (
                <div
                  key={channel.id}
                  onClick={() => setActiveChannel(channel.id)}
                  className={`flex items-center justify-between p-3 rounded-xl cursor-pointer transition-all duration-300 group ${
                    activeChannel === channel.id 
                      ? 'bg-cyan-500/20 border border-cyan-500/30 text-cyan-400' 
                      : 'text-gray-400 hover:bg-slate-800/60 hover:text-gray-200'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{channel.icon}</span>
                    <div>
                      <span className="text-sm font-medium">{channel.name.replace('# ', '')}</span>
                      <div className="text-xs text-gray-500">{channel.members.toLocaleString()} members</div>
                    </div>
                  </div>
                  {channel.unread > 0 && (
                    <div className="bg-orange-500 text-slate-900 text-xs px-2 py-1 min-w-[24px] h-6 flex items-center justify-center font-bold rounded-full">
                      {channel.unread}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Gaming Channels */}
            <div className="space-y-1 mb-6">
              <h3 className="text-xs font-bold text-purple-400 uppercase tracking-wide mb-3 flex items-center">
                <Trophy className="w-3 h-3 mr-1" />
                Gaming Hub
              </h3>
              {getChannelsByCategory('Gaming').map((channel) => (
                <div
                  key={channel.id}
                  onClick={() => setActiveChannel(channel.id)}
                  className={`flex items-center justify-between p-3 rounded-xl cursor-pointer transition-all duration-300 group ${
                    activeChannel === channel.id 
                      ? 'bg-purple-500/20 border border-purple-500/30 text-purple-400' 
                      : 'text-gray-400 hover:bg-slate-800/60 hover:text-gray-200'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{channel.icon}</span>
                    <div>
                      <span className="text-sm font-medium">{channel.name.replace('# ', '')}</span>
                      <div className="text-xs text-gray-500">{channel.members.toLocaleString()} members</div>
                    </div>
                  </div>
                  {channel.unread > 0 && (
                    <div className="bg-orange-500 text-slate-900 text-xs px-2 py-1 min-w-[24px] h-6 flex items-center justify-center font-bold rounded-full">
                      {channel.unread}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Online Users */}
            <div>
              <h3 className="text-xs font-bold text-green-400 uppercase tracking-wide mb-3 flex items-center">
                <Users className="w-3 h-3 mr-1" />
                Legends Online — {onlineUsers.length}
              </h3>
              <div className="space-y-2">
                {onlineUsers.map((user, index) => (
                  <div key={index} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-slate-800/60 cursor-pointer transition-all duration-300 group">
                    <div className="relative">
                      <div className="w-10 h-10 rounded-xl bg-slate-800 flex items-center justify-center text-lg border border-slate-700">
                        {user.avatar}
                      </div>
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-slate-900 flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-semibold truncate text-gray-200">{user.name}</p>
                        <span className="text-xs text-gray-500">Lv.{user.level}</span>
                      </div>
                      <p className={`text-xs truncate ${getRoleColor(user.role)}`}>{user.status}</p>
                      <p className="text-xs text-gray-600">{user.role}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col bg-slate-900/95">
          {/* Chat Header */}
          <div className="bg-slate-900/95 border-b border-slate-800 p-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-2xl">{channels.find(c => c.id === activeChannel)?.icon}</div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  #{activeChannel}
                </h1>
                <div className="text-gray-400 text-sm flex items-center gap-2">
                  <span>{channels.find(c => c.id === activeChannel)?.members.toLocaleString()} members</span>
                  <span>•</span>
                  <span className="text-green-400">{communityStats.onlineNow} online</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="p-2 hover:bg-slate-800 rounded-lg transition-colors">
                <Search className="h-4 w-4 text-gray-400" />
              </button>
              <button className="p-2 hover:bg-slate-800 rounded-lg transition-colors">
                <Filter className="h-4 w-4 text-gray-400" />
              </button>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-6 bg-slate-900/50">
            {messages.map((msg) => (
              <div key={msg.id} className={`group relative ${msg.pinned ? 'bg-orange-500/10 border border-orange-500/30 rounded-xl p-4' : ''}`}>
                {msg.pinned && (
                  <div className="flex items-center space-x-2 mb-3 text-orange-400 text-sm font-medium">
                    <Pin className="h-4 w-4" />
                    <Crown className="h-4 w-4" />
                    <span>Pinned Announcement</span>
                  </div>
                )}
                
                <div className="flex items-start space-x-4">
                  <div className="relative">
                    <div className="w-12 h-12 rounded-xl bg-slate-800 flex items-center justify-center text-xl border border-slate-700">
                      {msg.avatar}
                    </div>
                    {msg.staff && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                        <Crown className="w-3 h-3 text-slate-900" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className={`font-bold text-lg ${msg.staff ? 'text-orange-400' : 'text-gray-200'}`}>
                        {msg.user}
                      </span>
                      
                      {msg.level && (
                        <span className="text-xs bg-slate-800/60 text-cyan-400 px-2 py-1 rounded-full font-medium">
                          Lv.{msg.level}
                        </span>
                      )}
                      
                      {msg.staff && (
                        <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-slate-900 text-xs px-3 py-1 font-bold rounded-full flex items-center">
                          <Crown className="w-3 h-3 mr-1" />
                          STAFF
                        </div>
                      )}
                      
                      {msg.badges && (
                        <div className="flex space-x-1">
                          {msg.badges.map((badge, index) => (
                            <span key={index} className="text-sm" title="Achievement Badge">
                              {badge}
                            </span>
                          ))}
                        </div>
                      )}
                      
                      <span className="text-xs text-gray-500">{msg.timestamp}</span>
                    </div>
                    
                    <p className="text-gray-300 mb-3 leading-relaxed break-words">{msg.message}</p>
                    
                    {msg.reactions.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {msg.reactions.map((reaction, index) => (
                          <button
                            key={index}
                            onClick={() => handleReaction(msg.id, reaction.emoji)}
                            className="flex items-center space-x-2 bg-slate-800/60 border border-slate-700 rounded-full px-3 py-1 text-sm hover:bg-cyan-500/20 hover:border-cyan-500/50 transition-all duration-300"
                          >
                            <span className="text-base">{reaction.emoji}</span>
                            <span className="font-medium text-gray-300">{reaction.count}</span>
                          </button>
                        ))}
                        <button 
                          className="flex items-center justify-center w-8 h-8 bg-slate-800/60 border border-slate-700 rounded-full text-sm hover:bg-cyan-500/20 hover:border-cyan-500/50 transition-all duration-300 opacity-0 group-hover:opacity-100"
                          onClick={() => handleReaction(msg.id, '👍')}
                        >
                          <Smile className="h-4 w-4 text-gray-400" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="p-6 border-t border-slate-800 bg-slate-900/95">
            {isConnected ? (
              <div className="flex items-end space-x-4">
                <div className="flex-1">
                  <textarea
                    placeholder={`Share your epic gaming moments in #${activeChannel}...`}
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    className="w-full resize-none bg-slate-800/60 border border-slate-700 focus:border-cyan-500 max-h-32 rounded-xl text-gray-200 placeholder:text-gray-500 p-3 outline-none transition-colors"
                    rows={1}
                  />
                </div>
                <button 
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-bold p-3 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="h-5 w-5" />
                </button>
              </div>
            ) : (
              <div className="text-center p-8 bg-slate-900/90 border border-slate-800 rounded-2xl">
                <div className="w-16 h-16 bg-cyan-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageCircle className="h-8 w-8 text-cyan-400" />
                </div>
                <h3 className="text-2xl font-bold mb-2 text-white">
                  Join the Epic Conversation
                </h3>
                <p className="text-gray-400 mb-6 max-w-md mx-auto leading-relaxed">
                  Connect your wallet to participate in community discussions, earn social points, and become part of the gaming legend.
                </p>
                <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 text-orange-400" />
                    <span>Earn Social Points</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Trophy className="w-4 h-4 text-cyan-400" />
                    <span>Join Tournaments</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Crown className="w-4 h-4 text-purple-400" />
                    <span>Build Reputation</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Community;