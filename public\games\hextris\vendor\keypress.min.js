/*
  Keypress version 1.0.8 (c) 2013 <PERSON>.
  Licensed under the Apache License, Version 2.0
  http://www.apache.org/licenses/LICENSE-2.0
*/
(function(){var w,x,j,R,S,T,U,y,z,A,V,H,I,B,m,W,X,Y,Z,J,$,aa,ba,ca,da,K,ea,r,s,g,L,t,fa,M,u,N,C,v,O,D,h,P,E,p,F,Q,G,ga,i=[].indexOf||function(a){for(var c=0,b=this.length;c<b;c++)if(c in this&&this[c]===a)return c;return-1},ha={}.hasOwnProperty;h=[];p=[];F=null;g=[];j=[];C=!1;M="ctrl";N="meta alt option ctrl shift cmd".split(" ");G=[];z={keys:[],count:0};B=function(a,c){var b;if(a.filter)return a.filter(c);var d,e,f;f=[];d=0;for(e=a.length;d<e;d++)b=a[d],c(b)&&f.push(b);return f};t=function(){return console.log.apply(console,
arguments)};A=function(a,c){var b,d,e;if(a.length!==c.length)return!1;d=0;for(e=a.length;d<e;d++)if(b=a[d],!(0<=i.call(c,b)))return!1;return!0};V=function(a,c){var b,d,e;if(a.length!==c.length)return!1;b=d=0;for(e=a.length;0<=e?d<e:d>e;b=0<=e?++d:--d)if(a[b]!==c[b])return!1;return!0};ba=function(a,c){var b,d,e;d=0;for(e=a.length;d<e;d++)if(b=a[d],0>i.call(c,b))return!1;return!0};ca=function(a,c){var b,d,e,f;e=d=0;for(f=a.length;e<f;e++)if(b=a[e],b=c.indexOf(b),b>=d)d=b;else return!1;return!0};v=function(a,
c){if((c||keypress.suppress_event_defaults)&&!keypress.force_event_defaults)if(a.preventDefault?a.preventDefault():a.returnValue=!1,a.stopPropagation)return a.stopPropagation()};T=function(a){if(a.prevent_repeat)return!1;if("function"===typeof a.on_keydown)return!0};L=function(a){var c,b,d,e;e=a.keys;b=0;for(d=e.length;b<d;b++)if(a=e[b],0<=i.call(g,a)){c=!0;break}return c};m=function(a,c,b){"function"===typeof c["on_"+a]&&v(b,!1===c["on_"+a].call(c["this"],b,c.count));"release"===a&&(c.count=0);if("keyup"===
a)return c.keyup_fired=!0};fa=function(a,c){var b,d,e;d=0;for(e=h.length;d<e;d++)b=h[d],(b.is_ordered&&V(a,b.keys)||!b.is_ordered&&A(a,b.keys))&&c(b)};W=function(a,c){var b,d,e;d=0;for(e=h.length;d<e;d++)b=h[d],(b.is_ordered&&ca(b.keys,a)||!b.is_ordered&&ba(b.keys,a))&&c(b)};y=function(a){return 0<=i.call(g,"cmd")&&0>i.call(a,"cmd")?!1:!0};X=function(a){var c,b;c=[];b=B(g,function(b){return b!==a});b.push(a);fa(b,function(a){if(y(a.keys))return c.push(a)});W(b,function(a){if(!(0<=i.call(c,a))&&!a.is_solitary&&
y(a.keys))return c.push(a)});return c};Z=function(a){var c,b,d,e;b=[];d=0;for(e=h.length;d<e;d++)c=h[d],c.is_sequence||0<=i.call(c.keys,a)&&y(c.keys)&&b.push(c);return b};S=function(a){var c,b,d,e,f,q,g,h,l,k,o;q=!1;f=!0;d=!1;if(0<=i.call(j,a))return!0;if(j.length){e=g=0;for(k=j.length;0<=k?g<k:g>k;e=0<=k?++g:--g)if((c=j[e])&&c.is_exclusive&&a.is_exclusive){c=c.keys;if(!q){h=0;for(l=c.length;h<l;h++)if(b=c[h],q=!0,0>i.call(a.keys,b)){q=!1;break}}if(f&&!q){o=a.keys;h=0;for(l=o.length;h<l;h++)if(b=
o[h],f=!1,0>i.call(c,b)){f=!0;break}}q&&(d?E(j.splice(e,1)):(E(j.splice(e,1,a)),d=!0),f=!1)}}f&&j.unshift(a);return q||f};P=function(a){var c,b,d,e;b=d=0;for(e=j.length;0<=e?d<e:d>e;b=0<=e?++d:--d)if(c=j[b],c===a){E(j.splice(b,1));break}};E=function(a){if(a)return a.count=null,a.keyup_fired=null};R=function(a,c){var b,d,e,f;p.push(a);d=Y();if(d.length){e=0;for(f=d.length;e<f;e++)b=d[e],v(c,b.prevent_default);F&&clearTimeout(F);-1<keypress.sequence_delay&&(F=setTimeout(function(){return p=[]},keypress.sequence_delay))}else p=
[]};Y=function(){var a,c,b,d,e,f,g,n,j,l,k;d=[];f=0;for(j=h.length;f<j;f++){a=h[f];c=g=1;for(l=p.length;1<=l?g<=l:g>=l;c=1<=l?++g:--g)if(e=p.slice(-c),a.is_sequence){if(0>i.call(a.keys,"shift")&&(e=B(e,function(a){return"shift"!==a}),!e.length))continue;c=n=0;for(k=e.length;0<=k?n<k:n>k;c=0<=k?++n:--n)if(a.keys[c]===e[c])b=!0;else{b=!1;break}b&&d.push(a)}}return d};J=function(a){var c,b,d,e,f,g,j,m,l,k,o;g=0;for(l=h.length;g<l;g++)if(c=h[g],c.is_sequence){b=j=1;for(k=p.length;1<=k?j<=k:j>=k;b=1<=
k?++j:--j)if(f=B(p,function(a){return 0<=i.call(c.keys,"shift")?!0:"shift"!==a}).slice(-b),c.keys.length===f.length){b=m=0;for(o=f.length;0<=o?m<o:m>o;b=0<=o?++m:--m)if(e=f[b],!(0>i.call(c.keys,"shift")&&"shift"===e)&&!("shift"===a&&0>i.call(c.keys,"shift")))if(c.keys[b]===e)d=!0;else{d=!1;break}}if(d)return c}return!1};I=function(a,c){var b;if(!c.shiftKey)return!1;b=s[a];return null!=b?b:!1};$=function(a,c,b){if(0>i.call(a.keys,c))return!1;v(b,a&&a.prevent_default);if(0<=i.call(g,c)&&!T(a))return!1;
c=S(a,c);a.keyup_fired=!1;a.is_counting&&"function"===typeof a.on_keydown&&(a.count+=1);if(c)return m("keydown",a,b)};da=function(a,c){var b,d,e,f;(d=I(a,c))&&(a=d);R(a,c);(d=J(a))&&m("keydown",d,c);for(b in u)d=u[b],c[d]&&(b===a||0<=i.call(g,b)||g.push(b));for(b in u)if(d=u[b],b!==a&&0<=i.call(g,b)&&!c[d]){d=e=0;for(f=g.length;0<=f?e<f:e>f;d=0<=f?++e:--e)g[d]===b&&g.splice(d,1)}d=X(a);e=0;for(f=d.length;e<f;e++)b=d[e],$(b,a,c);d=Z(a);if(d.length){e=0;for(f=d.length;e<f;e++)b=d[e],v(c,b.prevent_default)}0>
i.call(g,a)&&g.push(a)};aa=function(a,c,b){var d,e;e=L(a);if(!a.keyup_fired&&(d=g.slice(),d.push(b),!a.is_solitary||A(d,a.keys)))m("keyup",a,c),a.is_counting&&("function"===typeof a.on_keyup&&"function"!==typeof a.on_keydown)&&(a.count+=1);e||(m("release",a,c),P(a))};K=function(a,c){var b,d,e,f,h,n;d=a;(e=I(a,c))&&(a=e);e=s[d];c.shiftKey?e&&0<=i.call(g,e)||(a=d):d&&0<=i.call(g,d)||(a=e);(f=J(a))&&m("keyup",f,c);if(0>i.call(g,a))return!1;f=h=0;for(n=g.length;0<=n?h<n:h>n;f=0<=n?++h:--h)if((b=g[f])===
a||b===e||b===d){g.splice(f,1);break}d=j.length;e=[];f=0;for(h=j.length;f<h;f++)b=j[f],0<=i.call(b.keys,a)&&e.push(b);f=0;for(h=e.length;f<h;f++)b=e[f],aa(b,c,a);if(1<d){d=0;for(f=j.length;d<f;d++)b=j[d],void 0===b||0<=i.call(e,b)||L(b)||P(b)}};D=function(a,c){var b;if(C)g.length&&(g=[]);else if(c||g.length)if(b=H(a.keyCode))return c?da(b,a):K(b,a)};Q=function(a){var c,b,d,e;e=[];c=b=0;for(d=h.length;0<=d?b<d:b>d;c=0<=d?++b:--b)if(a===h[c]){h.splice(c,1);break}else e.push(void 0);return e};ga=function(a){var c,
b,d,e,f;a.keys.length||t("You're trying to bind a combo with no keys.");b=e=0;for(f=a.keys.length;0<=f?e<f:e>f;b=0<=f?++e:--e)d=a.keys[b],(c=ea[d])&&(d=a.keys[b]=c),"meta"===d&&a.keys.splice(b,1,M),"cmd"===d&&t('Warning: use the "meta" key rather than "cmd" for Windows compatibility');f=a.keys;c=0;for(e=f.length;c<e;c++)if(d=f[c],0>i.call(G,d))return t('Do not recognize the key "'+d+'"'),!1;if(0<=i.call(a.keys,"meta")||0<=i.call(a.keys,"cmd")){c=a.keys.slice();e=0;for(f=N.length;e<f;e++)d=N[e],-1<
(b=c.indexOf(d))&&c.splice(b,1);1<c.length&&t("META and CMD key combos cannot have more than 1 non-modifier keys",a,c)}return!0};U=function(a){var c;if(0<=i.call(g,"cmd")&&"cmd"!==(c=H(a.keyCode))&&"shift"!==c&&"alt"!==c&&"caps"!==c&&"tab"!==c)return D(a,!1)};window.keypress={};keypress.force_event_defaults=!1;keypress.suppress_event_defaults=!1;keypress.sequence_delay=800;keypress.get_registered_combos=function(){return h};keypress.reset=function(){h=[]};keypress.combo=function(a,c,b){null==b&&(b=
!1);return keypress.register_combo({keys:a,on_keydown:c,prevent_default:b})};keypress.counting_combo=function(a,c,b){null==b&&(b=!1);return keypress.register_combo({keys:a,is_counting:!0,is_ordered:!0,on_keydown:c,prevent_default:b})};keypress.sequence_combo=function(a,c,b){null==b&&(b=!1);return keypress.register_combo({keys:a,on_keydown:c,is_sequence:!0,prevent_default:b})};keypress.register_combo=function(a){var c,b;"string"===typeof a.keys&&(a.keys=a.keys.split(" "));for(c in z)ha.call(z,c)&&
(b=z[c],null==a[c]&&(a[c]=b));if(ga(a))return h.push(a),!0};keypress.register_many=function(a){var c,b,d,e;e=[];b=0;for(d=a.length;b<d;b++)c=a[b],e.push(keypress.register_combo(c));return e};keypress.unregister_combo=function(a){var c,b,d;if(!a)return!1;if(a.keys)return Q(a);d=[];c=0;for(b=h.length;c<b;c++)(a=h[c])&&(A(keys,a.keys)?d.push(Q(a)):d.push(void 0));return d};keypress.unregister_many=function(a){var c,b,d,e;e=[];b=0;for(d=a.length;b<d;b++)c=a[b],e.push(keypress.unregister_combo(c));return e};
keypress.listen=function(){return C=!1};keypress.stop_listening=function(){return C=!0};H=function(a){return r[a]};u={cmd:"metaKey",ctrl:"ctrlKey",shift:"shiftKey",alt:"altKey"};ea={escape:"esc",control:"ctrl",command:"cmd","break":"pause",windows:"cmd",option:"alt",caps_lock:"caps",apostrophe:"'",semicolon:";",tilde:"~",accent:"`",scroll_lock:"scroll",num_lock:"num"};s={"/":"?",".":">",",":"<","'":'"',";":":","[":"{","]":"}","\\":"|","`":"~","=":"+","-":"_",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",
8:"*",9:"(","0":")"};r={"0":"\\",8:"backspace",9:"tab",12:"num",13:"enter",16:"shift",17:"ctrl",18:"alt",19:"pause",20:"caps",27:"escape",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",44:"print",45:"insert",46:"delete",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",65:"a",66:"b",67:"c",68:"d",69:"e",70:"f",71:"g",72:"h",73:"i",74:"j",75:"k",76:"l",77:"m",78:"n",79:"o",80:"p",81:"q",82:"r",83:"s",84:"t",85:"u",86:"v",87:"w",88:"x",
89:"y",90:"z",91:"cmd",92:"cmd",93:"cmd",96:"num_0",97:"num_1",98:"num_2",99:"num_3",100:"num_4",101:"num_5",102:"num_6",103:"num_7",104:"num_8",105:"num_9",106:"num_multiply",107:"num_add",108:"num_enter",109:"num_subtract",110:"num_decimal",111:"num_divide",124:"print",144:"num",145:"scroll",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",223:"`",224:"cmd",57392:"ctrl",63289:"num"};for(x in r)w=r[x],G.push(w);for(x in s)w=s[x],G.push(w);-1!==navigator.userAgent.indexOf("Mac OS X")&&
(M="cmd");-1!==navigator.userAgent.indexOf("Opera")&&(r["17"]="cmd");O=function(a){return(document.attachEvent?"complete"===document.readyState:"loading"!==document.readyState)?a():setTimeout(function(){return O(a)},9)};O(function(){var a;a=function(a,b,d){if(a.addEventListener)return a.addEventListener(b,d);if(a.attachEvent)return a.attachEvent("on"+b,d)};a(document.body,"keydown",function(a){a=a||window.event;D(a,!0);return U(a)});a(document.body,"keyup",function(a){a=a||window.event;return D(a,
!1)});return a(window,"blur",function(){var a,b,d;b=0;for(d=g.length;b<d;b++)a=g[b],K(a,{});g=[];return[]})})}).call(this);
